# ImageMagick RGB565转换命令大全 - Windows版本

## 🔧 基础命令

### 1. 检查ImageMagick版本
magick -version

### 2. 查看图片信息
magick identify dandelion_flower.png
magick identify tulip_flower.png

### 3. 查看支持的格式
magick -list format | findstr RGB

## 🎯 RGB565转换命令

### 方法1：直接转换为RGB565格式 (推荐)
# 转换为64x64缩略图
magick dandelion_flower.png -resize 64x64! -depth 16 RGB565:dandelion_thumb.bin
magick tulip_flower.png -resize 64x64! -depth 16 RGB565:tulip_thumb.bin

# 转换为150x150预览图
magick dandelion_flower.png -resize 150x150! -depth 16 RGB565:dandelion_preview.bin
magick tulip_flower.png -resize 150x150! -depth 16 RGB565:tulip_preview.bin

# 转换为296x240全屏图
magick dandelion_flower.png -resize 296x240! -depth 16 RGB565:dandelion_full.bin
magick tulip_flower.png -resize 296x240! -depth 16 RGB565:tulip_full.bin

### 方法2：保持宽高比转换
# 保持比例，最大64x64
magick dandelion_flower.png -resize 64x64 -depth 16 RGB565:dandelion_thumb_ratio.bin
magick tulip_flower.png -resize 64x64 -depth 16 RGB565:tulip_thumb_ratio.bin

### 方法3：添加背景色
# 转换时添加白色背景
magick dandelion_flower.png -background white -resize 64x64! -depth 16 RGB565:dandelion_white_bg.bin
magick tulip_flower.png -background white -resize 64x64! -depth 16 RGB565:tulip_white_bg.bin

# 转换时添加黑色背景
magick dandelion_flower.png -background black -resize 64x64! -depth 16 RGB565:dandelion_black_bg.bin
magick tulip_flower.png -background black -resize 64x64! -depth 16 RGB565:tulip_black_bg.bin

## 📐 常用尺寸转换

### 缩略图尺寸 (推荐用于列表显示)
magick dandelion_flower.png -resize 32x32! -depth 16 RGB565:dandelion_32x32.bin
magick dandelion_flower.png -resize 48x48! -depth 16 RGB565:dandelion_48x48.bin
magick dandelion_flower.png -resize 64x64! -depth 16 RGB565:dandelion_64x64.bin

magick tulip_flower.png -resize 32x32! -depth 16 RGB565:tulip_32x32.bin
magick tulip_flower.png -resize 48x48! -depth 16 RGB565:tulip_48x48.bin
magick tulip_flower.png -resize 64x64! -depth 16 RGB565:tulip_64x64.bin

### 中等尺寸 (推荐用于详情显示)
magick dandelion_flower.png -resize 100x100! -depth 16 RGB565:dandelion_100x100.bin
magick dandelion_flower.png -resize 128x128! -depth 16 RGB565:dandelion_128x128.bin
magick dandelion_flower.png -resize 150x150! -depth 16 RGB565:dandelion_150x150.bin

magick tulip_flower.png -resize 100x100! -depth 16 RGB565:tulip_100x100.bin
magick tulip_flower.png -resize 128x128! -depth 16 RGB565:tulip_128x128.bin
magick tulip_flower.png -resize 150x150! -depth 16 RGB565:tulip_150x150.bin

### LCD全屏尺寸 (296x240)
magick dandelion_flower.png -resize 296x240! -depth 16 RGB565:dandelion_fullscreen.bin
magick tulip_flower.png -resize 296x240! -depth 16 RGB565:tulip_fullscreen.bin

## 🔄 批量转换命令

### 批量转换所有PNG为64x64缩略图
for %f in (*.png) do magick "%f" -resize 64x64! -depth 16 RGB565:"%~nf_thumb.bin"

### 批量转换所有PNG为150x150预览图
for %f in (*.png) do magick "%f" -resize 150x150! -depth 16 RGB565:"%~nf_preview.bin"

### 批量转换所有PNG为全屏图
for %f in (*.png) do magick "%f" -resize 296x240! -depth 16 RGB565:"%~nf_full.bin"

## 📊 文件大小计算

### 不同尺寸的文件大小
32x32   = 32 × 32 × 2 = 2,048 字节 (2KB)
48x48   = 48 × 48 × 2 = 4,608 字节 (4.5KB)
64x64   = 64 × 64 × 2 = 8,192 字节 (8KB)
100x100 = 100 × 100 × 2 = 20,000 字节 (20KB)
128x128 = 128 × 128 × 2 = 32,768 字节 (32KB)
150x150 = 150 × 150 × 2 = 45,000 字节 (45KB)
296x240 = 296 × 240 × 2 = 142,080 字节 (139KB)

## 🎨 图像质量优化

### 增强对比度
magick dandelion_flower.png -resize 64x64! -contrast-stretch 2% -depth 16 RGB565:dandelion_enhanced.bin

### 调整亮度
magick dandelion_flower.png -resize 64x64! -brightness-contrast 10x5 -depth 16 RGB565:dandelion_bright.bin

### 锐化处理
magick dandelion_flower.png -resize 64x64! -unsharp 0x1 -depth 16 RGB565:dandelion_sharp.bin

## 🔍 验证转换结果

### 检查生成的bin文件大小
dir *.bin

### 将bin文件转换回PNG查看效果 (验证用)
magick -size 64x64 -depth 16 RGB565:dandelion_thumb.bin dandelion_check.png
magick -size 150x150 -depth 16 RGB565:dandelion_preview.bin dandelion_preview_check.png

## 📝 推荐的转换流程

### 第1步：转换缩略图 (用于列表显示)
magick dandelion_flower.png -resize 64x64! -depth 16 RGB565:dandelion_thumb.bin
magick tulip_flower.png -resize 64x64! -depth 16 RGB565:tulip_thumb.bin

### 第2步：转换预览图 (用于详情页面)
magick dandelion_flower.png -resize 150x150! -depth 16 RGB565:dandelion_preview.bin
magick tulip_flower.png -resize 150x150! -depth 16 RGB565:tulip_preview.bin

### 第3步：验证文件大小
dir *.bin

### 第4步：重命名为项目格式
ren dandelion_thumb.bin flower_001_thumb.bin
ren dandelion_preview.bin flower_001_full.bin
ren tulip_thumb.bin flower_002_thumb.bin
ren tulip_preview.bin flower_002_full.bin

## ⚡ 快速命令模板

### 单个文件转换模板
magick [输入文件名] -resize [宽度]x[高度]! -depth 16 RGB565:[输出文件名].bin

### 示例：
magick my_flower.png -resize 64x64! -depth 16 RGB565:my_flower_thumb.bin

## 🚨 注意事项

1. 使用 "!" 强制拉伸到指定尺寸
2. 不使用 "!" 保持原始宽高比
3. RGB565格式每像素占用2字节
4. 建议缩略图不超过64x64 (8KB)
5. 预览图建议150x150 (45KB)
6. 全屏图使用296x240 (139KB)

## 📁 建议的文件命名规范

flower_001_thumb.bin    # 蒲公英缩略图
flower_001_full.bin     # 蒲公英预览图
flower_002_thumb.bin    # 郁金香缩略图
flower_002_full.bin     # 郁金香预览图

这样命名便于在ESP32代码中使用！
