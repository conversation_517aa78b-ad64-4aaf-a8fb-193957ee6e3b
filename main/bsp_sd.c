
#include "bsp_sd.h"

/**
 * @brief SD卡挂载信号量
 * @details 用于同步SD卡挂载状态的信号量句柄（当前未使用）
 */
SemaphoreHandle_t sd_card_mounted_semaphore = NULL;

/**
 * @brief SD卡初始化和文件系统挂载任务
 * @details 执行SD卡的完整初始化流程：
 *          1. 配置SDMMC主机和插槽参数
 *          2. 设置1线SD模式和引脚分配
 *          3. 挂载FAT32文件系统到/sdcard
 *          4. 错误处理和状态报告
 * @param pvParameters FreeRTOS任务参数（未使用）
 * @return 无
 * @note 硬件配置：
 *       - CLK: GPIO47
 *       - CMD: GPIO48
 *       - D0:  GPIO21
 *       - 1线模式，内部上拉使能
 */
void sd_card_task(void *pvParameters) {
    esp_err_t ret;

    // SD卡挂载配置
    esp_vfs_fat_sdmmc_mount_config_t mount_config = {
        .format_if_mount_failed = false,  // 挂载失败时不格式化，保护数据
        .max_files = 5,                   // 最大同时打开文件数
        .allocation_unit_size = 16 * 1024 // 16KB分配单元大小
    };

    sdmmc_card_t *card;                   // SD卡信息结构体
    const char mount_point[] = "/sdcard"; // 挂载点路径
    ESP_LOGI("SD", "Initializing SD card");
    ESP_LOGI("SD", "Using SDMMC peripheral");

    // SDMMC主机配置
    sdmmc_host_t host = SDMMC_HOST_DEFAULT();

    // SDMMC插槽配置
    sdmmc_slot_config_t slot_config = SDMMC_SLOT_CONFIG_DEFAULT();
    slot_config.width = 1;                                    // 1线SD模式
    slot_config.clk = BSP_SD_CLK;                            // 时钟引脚GPIO47
    slot_config.cmd = BSP_SD_CMD;                            // 命令引脚GPIO48
    slot_config.d0 = BSP_SD_D0;                              // 数据引脚GPIO21
    slot_config.flags |= SDMMC_SLOT_FLAG_INTERNAL_PULLUP;    // 启用内部上拉

    ESP_LOGI("SD", "Mounting filesystem");
    // 执行SD卡挂载
    ret = esp_vfs_fat_sdmmc_mount(mount_point, &host, &slot_config, &mount_config, &card);

    // 挂载结果处理
    if (ret != ESP_OK) {
        if (ret == ESP_FAIL) {
            ESP_LOGE("SD", "Failed to mount filesystem. ");
        } else {
            ESP_LOGE("SD", "Failed to initialize the card (%s). ", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGI("SD", "Filesystem mounted successfully");
        sdmmc_card_print_info(stdout, card);  // 打印SD卡详细信息

/*        // 可选：释放挂载完成信号量
        if (sd_card_mounted_semaphore != NULL) {
            xSemaphoreGive(sd_card_mounted_semaphore);
        } */
    }

    vTaskDelete(NULL); // 任务完成后删除自身
}


