/**
 * @file bsp_key.c
 * @brief 触摸按键驱动实现
 * @details 基于GPIO中断的触摸按键检测，支持双边沿采样实现长短按逻辑
 * <AUTHOR>
 * @date 2024
 */

#include "bsp_key.h"

/*********************************************************************
 * 本地常量定义
 */
static const char *TAG = "BSP_KEY";

// 按键GPIO引脚映射表
static const gpio_num_t key_gpio_map[TOUCH_KEY_MAX] = {
    KEY_TC0_GPIO,   // TC0
    KEY_TC1_GPIO,   // TC1  
    KEY_TC2_GPIO    // TC2
};

// 按键名称映射表（用于日志输出）
static const char *key_name_map[TOUCH_KEY_MAX] = {
    "TC0",
    "TC1", 
    "TC2"
};

/*********************************************************************
 * 本地变量定义
 */
static bool s_key_initialized = false;                    // 按键系统初始化标志
static key_config_t s_key_config;                         // 按键配置参数
static QueueHandle_t s_key_event_queue = NULL;            // 按键事件队列句柄

// 按键状态记录结构体（用于长短按检测）
typedef struct {
    bool is_pressed;                // 当前按键状态：true=按下，false=释放
    uint32_t press_time;           // 按键按下时的时间戳（毫秒）
    bool long_press_triggered;     // 长按事件是否已触发标志
} key_state_t;

static key_state_t s_key_states[TOUCH_KEY_MAX];           // 所有按键的状态记录数组

/*********************************************************************
 * 本地函数声明
 */
static void IRAM_ATTR key_isr_handler(void *arg);
static touch_key_t gpio_to_key(gpio_num_t gpio);
static uint32_t get_timestamp_ms(void);
static void key_process_event(touch_key_t key, bool is_press);

/*********************************************************************
 * 核心接口函数实现
 */

/**
 * @brief 获取默认按键配置（内部函数）
 * @details 提供系统默认的按键配置参数
 * @return key_config_t 默认配置结构体
 */
static key_config_t bsp_key_get_default_config(void)
{
    key_config_t config = {
        .debounce_time_ms = 50,         // 防抖时间：50毫秒
        .long_press_time_ms = 1000,     // 长按判定时间：1秒
        .enable_long_press = true,      // 启用长按检测功能
        .enable_repeat = false          // 禁用按键重复功能
    };
    return config;
}

/**
 * @brief 初始化触摸按键系统
 * @details 配置GPIO为输入模式，启用下拉电阻，设置双边沿中断
 *          创建事件队列用于按键事件管理，支持长短按检测
 * @param config 按键配置参数，传NULL使用默认配置
 * @return esp_err_t 初始化结果
 */
esp_err_t bsp_touch_key_init(const key_config_t *config)
{
    // 检查是否已经初始化，避免重复初始化
    if (s_key_initialized) {
        ESP_LOGW(TAG, "按键系统已经初始化");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "开始初始化触摸按键系统...");

    // 配置参数处理：使用传入配置或默认配置
    if (config == NULL) {
        s_key_config = bsp_key_get_default_config();    // 使用默认配置
    } else {
        s_key_config = *config;                         // 使用用户配置
    }

    // 创建按键事件队列，队列长度为10个事件
    s_key_event_queue = xQueueCreate(10, sizeof(key_event_t));
    if (s_key_event_queue == NULL) {
        ESP_LOGE(TAG, "创建按键事件队列失败");
        return ESP_ERR_NO_MEM;
    }

    // 初始化所有按键状态为默认值（全部清零）
    memset(s_key_states, 0, sizeof(s_key_states));

    // 配置GPIO参数结构体
    gpio_config_t io_conf = {
        .intr_type = GPIO_INTR_ANYEDGE,         // 双边沿中断（上升沿+下降沿）
        .mode = GPIO_MODE_INPUT,                // 设置为输入模式
        .pin_bit_mask = 0,                      // 引脚掩码，稍后设置
        .pull_down_en = GPIO_PULLDOWN_ENABLE,   // 启用内部下拉电阻（确保未触摸时为低电平）
        .pull_up_en = GPIO_PULLUP_DISABLE       // 禁用内部上拉电阻
    };

    // 遍历所有按键，将对应GPIO引脚添加到配置掩码中
    for (int i = 0; i < TOUCH_KEY_MAX; i++) {
        io_conf.pin_bit_mask |= (1ULL << key_gpio_map[i]);
    }

    // 应用GPIO配置
    esp_err_t ret = gpio_config(&io_conf);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GPIO配置失败: %s", esp_err_to_name(ret));
        vQueueDelete(s_key_event_queue);        // 清理已创建的队列
        return ret;
    }

    // 安装GPIO中断服务（全局中断服务，所有GPIO共享）
    ret = gpio_install_isr_service(0);
    if (ret != ESP_OK && ret != ESP_ERR_INVALID_STATE) {
        ESP_LOGE(TAG, "安装GPIO中断服务失败: %s", esp_err_to_name(ret));
        vQueueDelete(s_key_event_queue);
        return ret;
    }

    // 为每个按键GPIO添加专用的中断处理函数
    for (int i = 0; i < TOUCH_KEY_MAX; i++) {
        ret = gpio_isr_handler_add(key_gpio_map[i], key_isr_handler, (void *)key_gpio_map[i]);
        if (ret != ESP_OK) {
            ESP_LOGE(TAG, "添加%s中断处理函数失败: %s", key_name_map[i], esp_err_to_name(ret));
            // 失败时清理已成功添加的中断处理函数
            for (int j = 0; j < i; j++) {
                gpio_isr_handler_remove(key_gpio_map[j]);
            }
            vQueueDelete(s_key_event_queue);
            s_key_event_queue = NULL;
            return ret;
        }
    }

    // 标记初始化完成
    s_key_initialized = true;

    // 输出初始化成功信息和配置参数
    ESP_LOGI(TAG, "触摸按键初始化完成");
    ESP_LOGI(TAG, "配置: 防抖%lums, 长按%lums, 长按检测:%s",
             s_key_config.debounce_time_ms,
             s_key_config.long_press_time_ms,
             s_key_config.enable_long_press ? "启用" : "禁用");

    return ESP_OK;
}

/**
 * @brief 获取按键事件
 * @details 从事件队列中获取按键事件，支持超时等待
 *          事件类型包括：按下、释放、短按、长按
 * @param event 输出的按键事件结构体指针
 * @param timeout_ms 超时时间（毫秒），0表示不等待
 * @return esp_err_t 获取结果
 */
esp_err_t bsp_key_get_event(key_event_t *event, uint32_t timeout_ms)
{
    // 参数有效性检查
    if (!s_key_initialized || event == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    // 将毫秒超时时间转换为FreeRTOS时钟节拍
    TickType_t ticks = (timeout_ms == 0) ? 0 : pdMS_TO_TICKS(timeout_ms);

    // 从队列中接收按键事件，支持超时等待
    if (xQueueReceive(s_key_event_queue, event, ticks) == pdTRUE) {
        return ESP_OK;      // 成功获取到事件
    }

    return ESP_ERR_TIMEOUT; // 超时未获取到事件
}

/**
 * @brief 获取按键当前状态
 * @details 直接读取GPIO电平状态，不依赖事件队列
 *          高电平表示按键按下，低电平表示按键释放
 * @param key 按键编号（TC0/TC1/TC2）
 * @return bool 按键状态，true=按下，false=释放
 */
bool bsp_key_get_state(touch_key_t key)
{
    // 检查系统初始化状态和按键编号有效性
    if (!s_key_initialized || key >= TOUCH_KEY_MAX) {
        return false;
    }

    // 直接读取GPIO电平状态：1=按下，0=释放
    return gpio_get_level(key_gpio_map[key]) == 1;
}

/**
 * @brief 按键事件处理任务
 * @details 持续监听按键事件并输出日志
 * @param pvParameters 任务参数（未使用）
 */
static void key_event_task(void *pvParameters)
{
    key_event_t key_event;

    ESP_LOGI(TAG, "按键事件监听任务启动");

    // 任务主循环：持续监听和处理按键事件
    while (1) {
        // 从事件队列获取按键事件，100ms超时
        if (bsp_key_get_event(&key_event, 100) == ESP_OK) {
            // 根据事件类型确定事件名称字符串
            const char *event_name = "";
            switch (key_event.event) {
                case KEY_EVENT_PRESS:
                    event_name = "按下";
                    break;
                case KEY_EVENT_RELEASE:
                    event_name = "释放";
                    break;
                case KEY_EVENT_SHORT_PRESS:
                    event_name = "短按";
                    break;
                case KEY_EVENT_LONG_PRESS:
                    event_name = "长按";
                    break;
            }

            // 输出按键事件信息：按键名称、事件类型、时间戳、持续时间
            printf("按键事件: %s %s (时间戳:%lu, 持续时间:%lums)\n",
                   (key_event.key == TOUCH_KEY_TC0) ? "TC0" :
                   (key_event.key == TOUCH_KEY_TC1) ? "TC1" : "TC2",
                   event_name, key_event.timestamp, key_event.duration);
        }

        // 短暂延时，避免任务过度占用CPU资源
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}

/**
 * @brief 启动按键监听任务
 * @details 创建独立任务处理按键事件
 */
void bsp_key_start_task(void)
{
    // 检查按键系统是否已初始化
    if (!s_key_initialized) {
        ESP_LOGE(TAG, "按键系统未初始化，无法启动任务");
        return;
    }

    // 创建按键事件处理任务
    BaseType_t ret = xTaskCreate(
        key_event_task,           // 任务函数指针
        "key_event_task",         // 任务名称（用于调试）
        2048,                     // 任务栈大小（字节）
        NULL,                     // 传递给任务的参数
        5,                        // 任务优先级（0-24，数字越大优先级越高）
        NULL                      // 任务句柄（不需要保存）
    );

    // 检查任务创建结果
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "创建按键事件任务失败");
    } else {
        ESP_LOGI(TAG, "按键事件任务创建成功");
    }
}



/*********************************************************************
 * 本地函数实现
 */

/**
 * @brief GPIO中断服务函数
 * @details 在IRAM中执行，响应双边沿中断
 *          读取GPIO状态并调用事件处理函数
 * @param arg GPIO引脚号（作为void*传入）
 */
static void IRAM_ATTR key_isr_handler(void *arg)
{
    // 将传入的参数转换为GPIO引脚号
    gpio_num_t gpio = (gpio_num_t)(int)arg;
    // 根据GPIO引脚号查找对应的按键编号
    touch_key_t key = gpio_to_key(gpio);

    // 确保按键编号有效
    if (key < TOUCH_KEY_MAX) {
        // 读取当前GPIO电平状态：1=按下，0=释放
        bool current_state = gpio_get_level(gpio) == 1;
        // 调用事件处理函数处理按键状态变化
        key_process_event(key, current_state);
    }
}

/**
 * @brief GPIO转换为按键编号
 * @details 根据GPIO引脚号查找对应的按键编号
 * @param gpio GPIO引脚号
 * @return touch_key_t 按键编号，无效时返回TOUCH_KEY_MAX
 */
static touch_key_t gpio_to_key(gpio_num_t gpio)
{
    // 遍历按键GPIO映射表，查找匹配的GPIO引脚
    for (int i = 0; i < TOUCH_KEY_MAX; i++) {
        if (key_gpio_map[i] == gpio) {
            return (touch_key_t)i;      // 返回对应的按键编号
        }
    }
    return TOUCH_KEY_MAX;               // 未找到匹配项，返回无效值
}

/**
 * @brief 获取当前时间戳（毫秒）
 * @details 基于FreeRTOS系统时钟节拍计算毫秒时间戳
 * @return uint32_t 当前时间戳（毫秒）
 */
static uint32_t get_timestamp_ms(void)
{
    // 获取系统时钟节拍数并转换为毫秒
    return xTaskGetTickCount() * portTICK_PERIOD_MS;
}

/**
 * @brief 处理按键事件（核心事件处理函数）
 * @details 在中断上下文中调用，处理按键状态变化
 *          支持长短按检测，生成相应的按键事件
 * @param key 按键编号
 * @param is_press 当前按键状态：true=按下，false=释放
 */
static void key_process_event(touch_key_t key, bool is_press)
{
    // 获取当前时间戳
    uint32_t current_time = get_timestamp_ms();
    // 获取对应按键的状态记录指针
    key_state_t *state = &s_key_states[key];

    // 检测按键按下事件（从释放状态变为按下状态）
    if (is_press && !state->is_pressed) {
        // 更新按键状态记录
        state->is_pressed = true;               // 标记为按下状态
        state->press_time = current_time;       // 记录按下时间
        state->long_press_triggered = false;    // 重置长按触发标志

        // 构造按键按下事件
        key_event_t event = {
            .key = key,                         // 按键编号
            .event = KEY_EVENT_PRESS,           // 事件类型：按下
            .timestamp = current_time,          // 事件时间戳
            .duration = 0                       // 按下事件持续时间为0
        };

        // 将事件发送到队列（中断安全版本）
        xQueueSendFromISR(s_key_event_queue, &event, NULL);

        // 输出按键按下日志（中断安全版本）
        ESP_EARLY_LOGI(TAG, "按键 %s 按下", key_name_map[key]);

    } else if (!is_press && state->is_pressed) {
        // 检测按键释放事件（从按下状态变为释放状态）

        // 计算按键持续时间
        uint32_t duration = current_time - state->press_time;
        // 更新按键状态为释放
        state->is_pressed = false;

        // 构造按键释放事件
        key_event_t event = {
            .key = key,                         // 按键编号
            .event = KEY_EVENT_RELEASE,         // 事件类型：释放
            .timestamp = current_time,          // 事件时间戳
            .duration = duration                // 按键持续时间
        };

        // 发送释放事件到队列
        xQueueSendFromISR(s_key_event_queue, &event, NULL);

        // 根据持续时间判断长短按类型
        if (s_key_config.enable_long_press && duration >= s_key_config.long_press_time_ms) {
            // 持续时间超过长按阈值，且长按检测已启用
            if (!state->long_press_triggered) {
                // 如果长按事件还未在按住期间触发，现在触发
                event.event = KEY_EVENT_LONG_PRESS;
                xQueueSendFromISR(s_key_event_queue, &event, NULL);
                ESP_EARLY_LOGI(TAG, "按键 %s 长按 (%lums)", key_name_map[key], duration);
            } else {
                // 长按事件已在按住期间触发，这里只记录松开
                ESP_EARLY_LOGI(TAG, "按键 %s 长按结束 (%lums)", key_name_map[key], duration);
            }
        } else {
            // 持续时间未达到长按阈值，生成短按事件
            event.event = KEY_EVENT_SHORT_PRESS;
            xQueueSendFromISR(s_key_event_queue, &event, NULL);
            ESP_EARLY_LOGI(TAG, "按键 %s 短按 (%lums)", key_name_map[key], duration);
        }

        // 输出按键释放日志
        ESP_EARLY_LOGI(TAG, "按键 %s 释放", key_name_map[key]);
    }
    // 注意：忽略其他状态变化（如重复的按下或释放信号）
}
