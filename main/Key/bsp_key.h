/**
 * @file bsp_key.h
 * @brief 触摸按键驱动头文件
 * @details 基于GPIO中断的触摸按键检测，支持TC0/TC1/TC2三个触摸按键
 * <AUTHOR>
 * @date 2024
 */

#ifndef __BSP_KEY_H__
#define __BSP_KEY_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <string.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "driver/gpio.h"
#include "esp_log.h"
#include "esp_err.h"

/*********************************************************************
 * 触摸按键硬件引脚定义
 */
#define KEY_TC0_GPIO        3       // TC0触摸按键引脚
#define KEY_TC1_GPIO        18      // TC1触摸按键引脚  
#define KEY_TC2_GPIO        17      // TC2触摸按键引脚

/*********************************************************************
 * 按键事件定义
 */

/**
 * @brief 触摸按键枚举
 */
typedef enum {
    TOUCH_KEY_TC0 = 0,              // TC0按键
    TOUCH_KEY_TC1 = 1,              // TC1按键
    TOUCH_KEY_TC2 = 2,              // TC2按键
    TOUCH_KEY_MAX                   // 按键总数
} touch_key_t;

/**
 * @brief 按键事件类型
 */
typedef enum {
    KEY_EVENT_PRESS = 0,            // 按键按下（上升沿）
    KEY_EVENT_RELEASE = 1,          // 按键释放（下降沿）
    KEY_EVENT_SHORT_PRESS = 2,      // 短按事件
    KEY_EVENT_LONG_PRESS = 3        // 长按事件
} key_event_type_t;

/**
 * @brief 按键事件结构体
 */
typedef struct {
    touch_key_t key;                // 按键编号
    key_event_type_t event;         // 事件类型
    uint32_t timestamp;             // 事件时间戳（毫秒）
    uint32_t duration;              // 按键持续时间（毫秒，仅对释放事件有效）
} key_event_t;

/*********************************************************************
 * 按键配置参数
 */

/**
 * @brief 按键配置结构体
 */
typedef struct {
    uint32_t debounce_time_ms;      // 防抖时间（毫秒）
    uint32_t long_press_time_ms;    // 长按判定时间（毫秒）
    bool enable_long_press;         // 是否启用长按检测
    bool enable_repeat;             // 是否启用按键重复
} key_config_t;

/*********************************************************************
 * 公共接口函数声明 - 对外提供的主要API
 */

/**
 * @brief 初始化触摸按键
 * @details 配置GPIO中断，初始化按键检测系统
 * @param config 按键配置参数，传NULL使用默认配置
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - ESP_FAIL: 初始化失败
 */
esp_err_t bsp_touch_key_init(const key_config_t *config);

/**
 * @brief 获取按键事件
 * @details 从事件队列中获取按键事件，非阻塞方式
 * @param event 输出的按键事件
 * @param timeout_ms 超时时间（毫秒），0表示不等待
 * @return esp_err_t
 *         - ESP_OK: 获取到事件
 *         - ESP_ERR_TIMEOUT: 超时无事件
 */
esp_err_t bsp_key_get_event(key_event_t *event, uint32_t timeout_ms);

/**
 * @brief 获取按键状态
 * @details 直接读取按键当前状态
 * @param key 按键编号
 * @return bool
 *         - true: 按键按下
 *         - false: 按键释放
 */
bool bsp_key_get_state(touch_key_t key);

/**
 * @brief 启动按键监听任务
 * @details 创建独立任务处理按键事件，输出日志
 */
void bsp_key_start_task(void);

#ifdef __cplusplus
}
#endif

#endif /* __BSP_KEY_H__ */
