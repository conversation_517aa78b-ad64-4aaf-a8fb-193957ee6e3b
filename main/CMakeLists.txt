set(LCD_DIR ./LCD)
file(GLOB_RECURSE LCD_SRCS ${LCD_DIR}/*.cpp ${LCD_DIR}/*.c)

set(KEY_DIR ./Key)
file(GLOB_RECURSE KEY_SRCS ${KEY_DIR}/*.cpp ${KEY_DIR}/*.c)

set(UI_DIR ./ui)
file(GLOB_RECURSE UI_SRCS ${UI_DIR}/*.cpp ${UI_DIR}/*.c)


# set(requires "driver" "adc" "gpio")

idf_component_register( SRCS
                             "sd_test.c"
                             "bsp_sd.c"
                             ${LCD_SRCS}
                             ${KEY_SRCS}
                             ${UI_SRCS}
                INCLUDE_DIRS
                             ${LCD_DIR}
                             ${KEY_DIR}
                             ${UI_DIR}
                             ${UI_DIR}/generated
                             ${UI_DIR}/custom
                             "."
                REQUIRES
                             esp_lcd
                             esp_adc
                             esp_driver_spi
                             spiffs
                             fatfs
                             lvgl__lvgl
                            #  ${requires}
)

# 定义宏以使GUI Guider生成的代码使用简单的LVGL包含路径
target_compile_definitions(${COMPONENT_LIB} PRIVATE LV_LVGL_H_INCLUDE_SIMPLE)

# if (CONFIG_ENABLE_FACTORY_FW_DEBUG_LOG)
#     # to override the CONFIG_LOG_MAXIMUM_LEVEL for 'main'
#     # still need to call esp_log_level_set() for specific module.
#     # The ESP-IDF doc is obscure for this.
#     target_compile_definitions(${COMPONENT_LIB} PUBLIC "-DLOG_LOCAL_LEVEL=ESP_LOG_DEBUG")
# endif()