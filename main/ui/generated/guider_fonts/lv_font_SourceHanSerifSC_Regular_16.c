/*
*This Font Software is licensed under the SIL Open Font License, Version 1.1. 
*This license is available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 16 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_SOURCEHANSERIFSC_REGULAR_16
#define LV_FONT_SOURCEHANSERIFSC_REGULAR_16 1
#endif

#if LV_FONT_SOURCEHANSERIFSC_REGULAR_16

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x3e, 0x25, 0xf4, 0x3f, 0x22, 0xf1, 0xf, 0x0,
    0xc0, 0xa, 0x0, 0x80, 0x1, 0x0, 0x0, 0x4e,
    0x34, 0xe3,

    /* U+0022 "\"" */
    0x1e, 0x22, 0xe1, 0x1f, 0x33, 0xf2, 0xf, 0x22,
    0xf0, 0xe, 0x0, 0xe0, 0xa, 0x0, 0xb0, 0x4,
    0x0, 0x40,

    /* U+0023 "#" */
    0x0, 0x9, 0x0, 0x26, 0x0, 0x0, 0x90, 0x4,
    0x40, 0x0, 0x8, 0x0, 0x72, 0x0, 0xff, 0xff,
    0xff, 0xfc, 0x1, 0x65, 0x11, 0x91, 0x10, 0x7,
    0x10, 0x9, 0x0, 0x0, 0x90, 0x0, 0x80, 0x9,
    0xff, 0xff, 0xff, 0xf3, 0x12, 0x92, 0x26, 0x52,
    0x0, 0x8, 0x0, 0x61, 0x0, 0x2, 0x60, 0x8,
    0x0, 0x0, 0x44, 0x0, 0x90, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x80, 0x0, 0x0, 0x0, 0x90, 0x0,
    0x0, 0x6b, 0xd9, 0x80, 0x5, 0xb0, 0x80, 0xc7,
    0xa, 0x60, 0x80, 0x6a, 0xa, 0x90, 0x80, 0x0,
    0x5, 0xf5, 0x90, 0x0, 0x0, 0x7f, 0xf6, 0x0,
    0x0, 0x2, 0xdf, 0xc1, 0x0, 0x0, 0x82, 0xd9,
    0x0, 0x0, 0x80, 0x6d, 0xe, 0x30, 0x80, 0x4c,
    0xb, 0x70, 0x80, 0xa6, 0x1, 0x89, 0xcb, 0x50,
    0x0, 0x0, 0x80, 0x0, 0x0, 0x0, 0x80, 0x0,

    /* U+0025 "%" */
    0x4, 0x98, 0x50, 0x0, 0x0, 0x15, 0x0, 0x0,
    0xd0, 0xc, 0x10, 0x0, 0x8, 0x10, 0x0, 0x2d,
    0x0, 0xa5, 0x0, 0x4, 0x50, 0x0, 0x3, 0xc0,
    0x9, 0x60, 0x0, 0x80, 0x0, 0x0, 0x2d, 0x0,
    0xa4, 0x0, 0x81, 0x37, 0x60, 0x0, 0xc2, 0xc,
    0x0, 0x35, 0x1c, 0x6, 0x70, 0x1, 0x77, 0x20,
    0x9, 0x7, 0x70, 0x1d, 0x0, 0x0, 0x0, 0x7,
    0x10, 0x96, 0x0, 0xf0, 0x0, 0x0, 0x3, 0x60,
    0xa, 0x60, 0xf, 0x0, 0x0, 0x0, 0x90, 0x0,
    0x87, 0x0, 0xe0, 0x0, 0x0, 0x72, 0x0, 0x3,
    0xa0, 0x49, 0x0, 0x0, 0x27, 0x0, 0x0, 0x7,
    0x79, 0x10, 0x0, 0x7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0026 "&" */
    0x0, 0x1a, 0x8b, 0x70, 0x0, 0x0, 0x0, 0xb4,
    0x0, 0xe3, 0x0, 0x0, 0x0, 0xe1, 0x0, 0xb4,
    0x0, 0x0, 0x0, 0xc5, 0x0, 0xc0, 0x0, 0x0,
    0x0, 0x3d, 0x17, 0x10, 0x47, 0x76, 0x0, 0x1b,
    0xe2, 0x0, 0x5, 0x60, 0x4, 0xa0, 0x9d, 0x10,
    0x8, 0x10, 0xe, 0x20, 0xb, 0xc0, 0x9, 0x0,
    0x4f, 0x0, 0x0, 0xcc, 0x53, 0x0, 0x3f, 0x20,
    0x0, 0x1d, 0xd0, 0x0, 0xc, 0xc1, 0x0, 0x48,
    0xeb, 0x0, 0x1, 0x9d, 0xca, 0x50, 0x2c, 0xc7,

    /* U+0027 "'" */
    0x1e, 0x21, 0xf3, 0xf, 0x20, 0xe0, 0xa, 0x0,
    0x40,

    /* U+0028 "(" */
    0x0, 0x4, 0x20, 0x1, 0x90, 0x0, 0xb1, 0x0,
    0x4a, 0x0, 0xb, 0x30, 0x0, 0xf0, 0x0, 0x4c,
    0x0, 0x6, 0xb0, 0x0, 0x7a, 0x0, 0x7, 0xa0,
    0x0, 0x6b, 0x0, 0x3, 0xd0, 0x0, 0xe, 0x0,
    0x0, 0xa5, 0x0, 0x2, 0xb0, 0x0, 0x9, 0x30,
    0x0, 0xa, 0x0, 0x0, 0x22,

    /* U+0029 ")" */
    0x42, 0x0, 0x0, 0xa0, 0x0, 0x4, 0x80, 0x0,
    0xc, 0x20, 0x0, 0x68, 0x0, 0x2, 0xd0, 0x0,
    0xe, 0x20, 0x0, 0xd3, 0x0, 0xc, 0x50, 0x0,
    0xc5, 0x0, 0xd, 0x30, 0x0, 0xf1, 0x0, 0x3c,
    0x0, 0x7, 0x70, 0x0, 0xc0, 0x0, 0x66, 0x0,
    0x19, 0x0, 0x3, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x9, 0x40, 0x0, 0x0, 0xa, 0x40, 0x0,
    0x68, 0x7, 0x23, 0x92, 0x39, 0xaa, 0x8b, 0x81,
    0x0, 0x18, 0x80, 0x0, 0x0, 0xc3, 0x88, 0x0,
    0x6, 0xa0, 0x1e, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0xb, 0x0,
    0x0, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x0, 0xb,
    0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x4, 0xbb,
    0xbe, 0xbb, 0xb7, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0xb, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0x0, 0x9, 0x0, 0x0,

    /* U+002C "," */
    0x2e, 0x52, 0xf8, 0x6, 0x51, 0xa0, 0x71, 0x0,

    /* U+002D "-" */
    0x4d, 0xdd, 0xb0,

    /* U+002E "." */
    0x0, 0x3, 0xf6, 0x2e, 0x50,

    /* U+002F "/" */
    0x0, 0x0, 0x55, 0x0, 0x0, 0xa1, 0x0, 0x0,
    0xb0, 0x0, 0x3, 0x70, 0x0, 0x8, 0x30, 0x0,
    0xb, 0x0, 0x0, 0x19, 0x0, 0x0, 0x65, 0x0,
    0x0, 0xa0, 0x0, 0x0, 0xb0, 0x0, 0x4, 0x70,
    0x0, 0x9, 0x20, 0x0, 0xb, 0x0, 0x0, 0x29,
    0x0, 0x0, 0x74, 0x0, 0x0, 0xb0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x3a, 0x9a, 0x20, 0x0, 0x2d, 0x10, 0x2e,
    0x10, 0x9, 0x80, 0x0, 0xa7, 0x0, 0xf4, 0x0,
    0x6, 0xd0, 0x1f, 0x20, 0x0, 0x4f, 0x3, 0xf1,
    0x0, 0x3, 0xf1, 0x3f, 0x10, 0x0, 0x3f, 0x11,
    0xf2, 0x0, 0x4, 0xf0, 0xf, 0x40, 0x0, 0x6d,
    0x0, 0x98, 0x0, 0xa, 0x70, 0x2, 0xd1, 0x2,
    0xe1, 0x0, 0x3, 0xa8, 0xa2, 0x0,

    /* U+0031 "1" */
    0x0, 0x35, 0x0, 0x7a, 0xcc, 0x0, 0x0, 0x9c,
    0x0, 0x0, 0x9b, 0x0, 0x0, 0x9b, 0x0, 0x0,
    0x9b, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x9b, 0x0,
    0x0, 0x9b, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x9b,
    0x0, 0x0, 0x9c, 0x0, 0x6a, 0xee, 0xa6,

    /* U+0032 "2" */
    0x6, 0x89, 0xc6, 0x0, 0x8a, 0x0, 0x2f, 0x40,
    0xb4, 0x0, 0xb, 0x90, 0x0, 0x0, 0xb, 0x90,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x7b, 0x0,
    0x0, 0x3, 0xc0, 0x0, 0x0, 0x1b, 0x10, 0x0,
    0x0, 0xa1, 0x0, 0x0, 0x9, 0x20, 0x0, 0x0,
    0x76, 0x22, 0x22, 0x20, 0xff, 0xff, 0xff, 0xf2,

    /* U+0033 "3" */
    0x0, 0x88, 0x9c, 0x50, 0x9, 0x90, 0x2, 0xf3,
    0x5, 0x10, 0x0, 0xd7, 0x0, 0x0, 0x0, 0xf5,
    0x0, 0x0, 0x9, 0xa0, 0x0, 0x19, 0xd9, 0x10,
    0x0, 0x0, 0x6, 0xe2, 0x0, 0x0, 0x0, 0xba,
    0x0, 0x0, 0x0, 0x8d, 0x1a, 0x0, 0x0, 0x9b,
    0xd, 0x40, 0x2, 0xf4, 0x2, 0x87, 0x9b, 0x40,

    /* U+0034 "4" */
    0x0, 0x0, 0xa, 0x90, 0x0, 0x0, 0x5, 0xc9,
    0x0, 0x0, 0x2, 0x69, 0x90, 0x0, 0x0, 0x80,
    0x99, 0x0, 0x0, 0x61, 0x9, 0x90, 0x0, 0x34,
    0x0, 0x99, 0x0, 0x17, 0x0, 0x9, 0x90, 0x6,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x9, 0x90,
    0x0, 0x0, 0x0, 0x99, 0x0, 0x0, 0x0, 0x9,
    0x90, 0x0, 0x0, 0x0, 0x99, 0x0,

    /* U+0035 "5" */
    0x0, 0xff, 0xff, 0xfb, 0x0, 0x19, 0x22, 0x22,
    0x20, 0x2, 0x60, 0x0, 0x0, 0x0, 0x45, 0x0,
    0x0, 0x0, 0x5, 0xa9, 0xba, 0x20, 0x0, 0x2,
    0x0, 0x5f, 0x30, 0x0, 0x0, 0x0, 0xac, 0x0,
    0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x0, 0x7e,
    0x2, 0xd0, 0x0, 0x9, 0xb0, 0xe, 0x40, 0x2,
    0xf3, 0x0, 0x29, 0x88, 0xa2, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x5, 0x96, 0x0, 0x0, 0x1b, 0x60,
    0x0, 0x0, 0x1c, 0x30, 0x0, 0x0, 0xb, 0x70,
    0x0, 0x0, 0x4, 0xe0, 0x0, 0x0, 0x0, 0xa9,
    0x69, 0xb6, 0x0, 0xd, 0xb2, 0x1, 0xe7, 0x0,
    0xf4, 0x0, 0x6, 0xe0, 0xf, 0x50, 0x0, 0x3f,
    0x10, 0xe6, 0x0, 0x3, 0xf0, 0xa, 0xa0, 0x0,
    0x6d, 0x0, 0x2e, 0x20, 0xc, 0x50, 0x0, 0x3a,
    0x89, 0x50, 0x0,

    /* U+0037 "7" */
    0x1f, 0xff, 0xff, 0xfc, 0x2, 0x22, 0x22, 0x48,
    0x0, 0x0, 0x0, 0x82, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0x6, 0x60, 0x0, 0x0, 0xc, 0x10,
    0x0, 0x0, 0x4a, 0x0, 0x0, 0x0, 0xb4, 0x0,
    0x0, 0x2, 0xe0, 0x0, 0x0, 0x9, 0x80, 0x0,
    0x0, 0x1f, 0x20, 0x0, 0x0, 0x7c, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x6a, 0x8b, 0x50, 0x0, 0x5c, 0x0, 0xd,
    0x40, 0xa, 0x70, 0x0, 0x98, 0x0, 0xa9, 0x0,
    0xb, 0x50, 0x4, 0xf2, 0x1, 0xa0, 0x0, 0x6,
    0xe6, 0x70, 0x0, 0x0, 0x31, 0x3c, 0xa0, 0x0,
    0x67, 0x0, 0xc, 0xa0, 0xf, 0x20, 0x0, 0x5f,
    0x0, 0xf2, 0x0, 0x4, 0xe0, 0xb, 0x80, 0x0,
    0x98, 0x0, 0x19, 0x98, 0x97, 0x0,

    /* U+0039 "9" */
    0x0, 0x5a, 0x8a, 0x30, 0x0, 0x5c, 0x0, 0x2e,
    0x20, 0xe, 0x50, 0x0, 0xa9, 0x1, 0xf3, 0x0,
    0x6, 0xe0, 0x1f, 0x40, 0x0, 0x4f, 0x0, 0xe7,
    0x0, 0x4, 0xf0, 0x5, 0xe3, 0x0, 0xae, 0x0,
    0x4, 0xba, 0x8a, 0xb0, 0x0, 0x0, 0x0, 0xe4,
    0x0, 0x0, 0x0, 0x7c, 0x0, 0x0, 0x0, 0x4c,
    0x20, 0x0, 0x0, 0x6b, 0x10, 0x0, 0x3, 0x84,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x2e, 0x53, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xf6, 0x2e, 0x50,

    /* U+003B ";" */
    0x2e, 0x53, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x14, 0xf8, 0xa, 0x70, 0x83, 0x67,
    0x2, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x5, 0x0, 0x0, 0x0, 0x2b,
    0x70, 0x0, 0x0, 0x8a, 0x20, 0x0, 0x5, 0xb4,
    0x0, 0x0, 0xa, 0x70, 0x0, 0x0, 0x0, 0x7a,
    0x10, 0x0, 0x0, 0x0, 0x2a, 0x70, 0x0, 0x0,
    0x0, 0x5, 0xb4, 0x0, 0x0, 0x0, 0x0, 0x8a,
    0x0, 0x0, 0x0, 0x0, 0x20,

    /* U+003D "=" */
    0x4a, 0xaa, 0xaa, 0xaa, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4b, 0xbb, 0xbb, 0xbb, 0x70,

    /* U+003E ">" */
    0x50, 0x0, 0x0, 0x0, 0x5c, 0x40, 0x0, 0x0,
    0x0, 0x8a, 0x10, 0x0, 0x0, 0x2, 0xa7, 0x0,
    0x0, 0x0, 0x5, 0xb2, 0x0, 0x0, 0x8, 0xa1,
    0x0, 0x4, 0xb4, 0x0, 0x2, 0xa7, 0x0, 0x0,
    0x7a, 0x20, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x8, 0xe8, 0x0, 0x3a, 0xcc, 0x10,
    0x0, 0x8, 0x0, 0x0, 0xa0, 0x0, 0x2b, 0x0,
    0x7e, 0x50, 0xcc, 0x30, 0xd, 0x0, 0x0, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e, 0x40,
    0x2, 0xe4, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x6, 0x88, 0x88, 0x81, 0x0, 0x0,
    0x2, 0xb3, 0x0, 0x0, 0x19, 0x40, 0x0, 0x2a,
    0x10, 0x0, 0x0, 0x0, 0x90, 0x0, 0xa0, 0x0,
    0x5a, 0xa8, 0x40, 0x27, 0x5, 0x50, 0x6, 0x90,
    0xe, 0x30, 0x9, 0xb, 0x0, 0x1d, 0x0, 0xf,
    0x0, 0x9, 0x19, 0x0, 0x88, 0x0, 0x3d, 0x0,
    0x9, 0x37, 0x0, 0xb5, 0x0, 0x6a, 0x0, 0x8,
    0x46, 0x0, 0xd5, 0x0, 0x97, 0x0, 0x53, 0x29,
    0x0, 0xab, 0x26, 0xa8, 0x3, 0x70, 0xb, 0x0,
    0x1a, 0x92, 0x19, 0x85, 0x0, 0xa, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3b, 0x50, 0x0, 0x4,
    0x10, 0x0, 0x0, 0x0, 0x58, 0x87, 0x72, 0x0,
    0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x74, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x70, 0xe4, 0x0, 0x0,
    0x0, 0x3, 0x40, 0xaa, 0x0, 0x0, 0x0, 0x8,
    0x0, 0x5f, 0x0, 0x0, 0x0, 0x8, 0x0, 0xf,
    0x40, 0x0, 0x0, 0x3b, 0x88, 0x8d, 0x90, 0x0,
    0x0, 0x81, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x90,
    0x0, 0x1, 0xf4, 0x0, 0x2, 0x70, 0x0, 0x0,
    0xc9, 0x0, 0x7c, 0xb6, 0x0, 0x29, 0xdf, 0x93,

    /* U+0042 "B" */
    0x18, 0xcf, 0x89, 0xaa, 0x20, 0x0, 0x7, 0xf0,
    0x0, 0x6e, 0x10, 0x0, 0x7e, 0x0, 0x0, 0xf5,
    0x0, 0x7, 0xe0, 0x0, 0x2f, 0x30, 0x0, 0x7e,
    0x0, 0x1b, 0x80, 0x0, 0x7, 0xf8, 0x9d, 0x70,
    0x0, 0x0, 0x7e, 0x0, 0x6, 0xd2, 0x0, 0x7,
    0xe0, 0x0, 0xa, 0xc0, 0x0, 0x7e, 0x0, 0x0,
    0x7f, 0x0, 0x7, 0xe0, 0x0, 0x9, 0xd0, 0x0,
    0x7f, 0x0, 0x3, 0xf5, 0x1, 0x9d, 0xf8, 0x99,
    0xa3, 0x0,

    /* U+0043 "C" */
    0x0, 0x1, 0x8a, 0xa9, 0xa5, 0x0, 0x4, 0xe6,
    0x0, 0x0, 0xf1, 0x1, 0xe6, 0x0, 0x0, 0xc,
    0x10, 0x9d, 0x0, 0x0, 0x0, 0x40, 0xd, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x70, 0x0, 0x0, 0x0, 0x0, 0xda,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xd0, 0x0, 0x0,
    0x3, 0x10, 0x1f, 0x60, 0x0, 0x0, 0xa3, 0x0,
    0x5e, 0x50, 0x0, 0xd, 0x30, 0x0, 0x19, 0xa9,
    0x89, 0x60,

    /* U+0044 "D" */
    0x18, 0xcf, 0x89, 0x9a, 0x40, 0x0, 0x0, 0x7f,
    0x0, 0x1, 0xba, 0x0, 0x0, 0x7f, 0x0, 0x0,
    0xe, 0x60, 0x0, 0x7f, 0x0, 0x0, 0x7, 0xf0,
    0x0, 0x7f, 0x0, 0x0, 0x4, 0xf2, 0x0, 0x7e,
    0x0, 0x0, 0x2, 0xf5, 0x0, 0x7e, 0x0, 0x0,
    0x2, 0xf5, 0x0, 0x7e, 0x0, 0x0, 0x4, 0xf2,
    0x0, 0x7e, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x7f,
    0x0, 0x0, 0x1f, 0x50, 0x0, 0x7f, 0x0, 0x2,
    0xc8, 0x0, 0x19, 0xdf, 0x89, 0x99, 0x30, 0x0,

    /* U+0045 "E" */
    0x19, 0xcf, 0x99, 0x99, 0xd5, 0x0, 0x7f, 0x0,
    0x0, 0x86, 0x0, 0x7f, 0x0, 0x0, 0x56, 0x0,
    0x7f, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x0, 0x18,
    0x0, 0x0, 0x7f, 0x88, 0xa8, 0x0, 0x0, 0x7e,
    0x0, 0x28, 0x0, 0x0, 0x7e, 0x0, 0x18, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0xa, 0x0, 0x7f, 0x0, 0x0, 0x3a, 0x19,
    0xdf, 0x99, 0x99, 0xba,

    /* U+0046 "F" */
    0x19, 0xcf, 0x99, 0x99, 0xd6, 0x0, 0x7f, 0x0,
    0x0, 0x76, 0x0, 0x7f, 0x0, 0x0, 0x47, 0x0,
    0x7f, 0x0, 0x1, 0x0, 0x0, 0x7f, 0x0, 0x18,
    0x0, 0x0, 0x7f, 0x88, 0xa8, 0x0, 0x0, 0x7e,
    0x0, 0x28, 0x0, 0x0, 0x7e, 0x0, 0x8, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x0, 0x18,
    0xcf, 0x96, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x1, 0x8a, 0xa9, 0xa6, 0x0, 0x0, 0x4e,
    0x60, 0x0, 0xd, 0x40, 0x1, 0xe6, 0x0, 0x0,
    0xa, 0x40, 0x9, 0xd0, 0x0, 0x0, 0x3, 0x20,
    0xd, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x70, 0x0, 0x6,
    0x9b, 0xa5, 0xd, 0xa0, 0x0, 0x0, 0xe, 0x70,
    0x9, 0xd0, 0x0, 0x0, 0xe, 0x70, 0x1, 0xe6,
    0x0, 0x0, 0xe, 0x70, 0x0, 0x4e, 0x60, 0x0,
    0xe, 0x70, 0x0, 0x1, 0x9a, 0x99, 0xa6, 0x0,

    /* U+0048 "H" */
    0x18, 0xcf, 0x84, 0x0, 0x7b, 0xfa, 0x60, 0x7,
    0xf0, 0x0, 0x0, 0x4f, 0x20, 0x0, 0x7f, 0x0,
    0x0, 0x4, 0xf2, 0x0, 0x7, 0xf0, 0x0, 0x0,
    0x4f, 0x20, 0x0, 0x7f, 0x0, 0x0, 0x4, 0xf2,
    0x0, 0x7, 0xf8, 0x88, 0x88, 0xaf, 0x20, 0x0,
    0x7e, 0x0, 0x0, 0x4, 0xf2, 0x0, 0x7, 0xe0,
    0x0, 0x0, 0x4f, 0x20, 0x0, 0x7e, 0x0, 0x0,
    0x4, 0xf2, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x4f,
    0x20, 0x0, 0x7f, 0x0, 0x0, 0x4, 0xf2, 0x1,
    0x9d, 0xf9, 0x50, 0x8, 0xbf, 0xb7,

    /* U+0049 "I" */
    0x18, 0xcf, 0x95, 0x0, 0x7f, 0x0, 0x0, 0x7f,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x7f, 0x0, 0x0,
    0x7e, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0x7f, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x7f,
    0x0, 0x18, 0xcf, 0x95,

    /* U+004A "J" */
    0x0, 0x8b, 0xfa, 0x70, 0x0, 0x3f, 0x20, 0x0,
    0x3, 0xf2, 0x0, 0x0, 0x3f, 0x20, 0x0, 0x3,
    0xf2, 0x0, 0x0, 0x3f, 0x20, 0x0, 0x3, 0xf2,
    0x0, 0x0, 0x3f, 0x20, 0x0, 0x3, 0xf2, 0x0,
    0x0, 0x3f, 0x20, 0x0, 0x3, 0xf1, 0x0, 0x0,
    0x4f, 0x0, 0x0, 0x8, 0x80, 0x1, 0xfc, 0xa1,
    0x0, 0x2, 0x30, 0x0, 0x0,

    /* U+004B "K" */
    0x18, 0xcf, 0x94, 0x6, 0xae, 0x92, 0x0, 0x7e,
    0x0, 0x0, 0x92, 0x0, 0x0, 0x7e, 0x0, 0x7,
    0x40, 0x0, 0x0, 0x7e, 0x0, 0x47, 0x0, 0x0,
    0x0, 0x7e, 0x1, 0xb0, 0x0, 0x0, 0x0, 0x7e,
    0xb, 0xf2, 0x0, 0x0, 0x0, 0x7e, 0x81, 0xca,
    0x0, 0x0, 0x0, 0x7f, 0x30, 0x4f, 0x30, 0x0,
    0x0, 0x7e, 0x0, 0xb, 0xc0, 0x0, 0x0, 0x7e,
    0x0, 0x3, 0xf5, 0x0, 0x0, 0x7e, 0x0, 0x0,
    0xad, 0x0, 0x18, 0xcf, 0x94, 0x5, 0xbf, 0xb6,

    /* U+004C "L" */
    0x18, 0xcf, 0x95, 0x0, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x0, 0x0,
    0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0, 0x7e,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x0, 0x0, 0x11, 0x0, 0x7f, 0x0,
    0x0, 0x66, 0x0, 0x7f, 0x0, 0x0, 0x95, 0x19,
    0xdf, 0x99, 0x99, 0xe5,

    /* U+004D "M" */
    0x29, 0xee, 0x0, 0x0, 0x0, 0x9, 0xfa, 0x60,
    0xa, 0xf5, 0x0, 0x0, 0x0, 0xdf, 0x10, 0x0,
    0x8b, 0xb0, 0x0, 0x0, 0x58, 0xf1, 0x0, 0x8,
    0x5f, 0x10, 0x0, 0x9, 0x4f, 0x10, 0x0, 0x90,
    0xe8, 0x0, 0x2, 0x74, 0xf1, 0x0, 0x9, 0x8,
    0xe0, 0x0, 0x81, 0x4f, 0x10, 0x0, 0x90, 0x2f,
    0x40, 0x9, 0x4, 0xf1, 0x0, 0x9, 0x0, 0xbb,
    0x4, 0x50, 0x5f, 0x10, 0x0, 0x90, 0x5, 0xf1,
    0x90, 0x5, 0xf1, 0x0, 0x9, 0x0, 0xe, 0x98,
    0x0, 0x5f, 0x10, 0x0, 0x90, 0x0, 0x8f, 0x20,
    0x5, 0xf1, 0x2, 0x9d, 0x94, 0x2, 0xc0, 0x8,
    0xbf, 0xa6,

    /* U+004E "N" */
    0x28, 0xec, 0x0, 0x0, 0x8a, 0xd8, 0x0, 0xb,
    0xf7, 0x0, 0x0, 0x8, 0x0, 0x0, 0x97, 0xf2,
    0x0, 0x0, 0x80, 0x0, 0x9, 0xc, 0xc0, 0x0,
    0x8, 0x0, 0x0, 0x90, 0x2f, 0x70, 0x0, 0x80,
    0x0, 0x9, 0x0, 0x7f, 0x20, 0x8, 0x0, 0x0,
    0x90, 0x0, 0xcc, 0x0, 0x80, 0x0, 0x9, 0x0,
    0x2, 0xf6, 0x8, 0x0, 0x0, 0x90, 0x0, 0x7,
    0xf2, 0x80, 0x0, 0x9, 0x0, 0x0, 0xc, 0xb8,
    0x0, 0x0, 0x90, 0x0, 0x0, 0x2f, 0x80, 0x2,
    0x9d, 0x96, 0x0, 0x0, 0x78, 0x0,

    /* U+004F "O" */
    0x0, 0x2, 0xaa, 0xaa, 0x50, 0x0, 0x0, 0x6e,
    0x20, 0x0, 0xc9, 0x0, 0x2, 0xf4, 0x0, 0x0,
    0xf, 0x50, 0xa, 0xd0, 0x0, 0x0, 0x9, 0xe0,
    0xd, 0x90, 0x0, 0x0, 0x5, 0xf1, 0xf, 0x70,
    0x0, 0x0, 0x3, 0xf4, 0xf, 0x70, 0x0, 0x0,
    0x3, 0xf4, 0xd, 0x90, 0x0, 0x0, 0x5, 0xf1,
    0xa, 0xd0, 0x0, 0x0, 0x8, 0xe0, 0x2, 0xf3,
    0x0, 0x0, 0xe, 0x50, 0x0, 0x6d, 0x20, 0x0,
    0xb9, 0x0, 0x0, 0x3, 0xa9, 0x9a, 0x50, 0x0,

    /* U+0050 "P" */
    0x18, 0xcf, 0x88, 0x9a, 0x20, 0x0, 0x7f, 0x0,
    0x5, 0xe2, 0x0, 0x7f, 0x0, 0x0, 0xd7, 0x0,
    0x7f, 0x0, 0x0, 0xc9, 0x0, 0x7f, 0x0, 0x0,
    0xe6, 0x0, 0x7e, 0x0, 0x1a, 0xb0, 0x0, 0x7f,
    0x99, 0x94, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x0, 0x0, 0x0, 0x19,
    0xdf, 0xa7, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x2, 0x9a, 0xaa, 0x40, 0x0, 0x0, 0x4e,
    0x30, 0x1, 0xd7, 0x0, 0x0, 0xe5, 0x0, 0x0,
    0x1f, 0x30, 0x7, 0xe0, 0x0, 0x0, 0xb, 0xc0,
    0xc, 0xa0, 0x0, 0x0, 0x6, 0xf0, 0xf, 0x80,
    0x0, 0x0, 0x4, 0xf2, 0x1f, 0x70, 0x0, 0x0,
    0x2, 0xf5, 0xf, 0x80, 0x0, 0x0, 0x3, 0xf3,
    0xd, 0x90, 0x0, 0x0, 0x5, 0xf1, 0x9, 0xd0,
    0x0, 0x0, 0x9, 0xd0, 0x2, 0xf3, 0x0, 0x0,
    0xe, 0x50, 0x0, 0x7d, 0x10, 0x0, 0xab, 0x0,
    0x0, 0x6, 0xb8, 0x7b, 0x80, 0x0, 0x0, 0x0,
    0x9, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x19, 0xb9, 0x30,

    /* U+0052 "R" */
    0x18, 0xcf, 0x88, 0x9b, 0x50, 0x0, 0x0, 0x7f,
    0x0, 0x2, 0xe5, 0x0, 0x0, 0x7e, 0x0, 0x0,
    0xab, 0x0, 0x0, 0x7e, 0x0, 0x0, 0xb9, 0x0,
    0x0, 0x7e, 0x0, 0x6, 0xd1, 0x0, 0x0, 0x7f,
    0x89, 0xe7, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x3f,
    0x20, 0x0, 0x0, 0x7e, 0x0, 0xc, 0x90, 0x0,
    0x0, 0x7e, 0x0, 0x7, 0xe0, 0x0, 0x0, 0x7f,
    0x0, 0x3, 0xf2, 0x0, 0x0, 0x7f, 0x0, 0x0,
    0xe6, 0x0, 0x19, 0xdf, 0xa5, 0x0, 0x6c, 0x91,

    /* U+0053 "S" */
    0x0, 0x5b, 0xaa, 0xa3, 0x0, 0x5c, 0x0, 0x5,
    0xb0, 0xb, 0x60, 0x0, 0x2b, 0x0, 0xc9, 0x0,
    0x0, 0x10, 0x6, 0xf7, 0x0, 0x0, 0x0, 0x8,
    0xfe, 0x70, 0x0, 0x0, 0x2, 0x9f, 0xe3, 0x0,
    0x0, 0x0, 0x1b, 0xe0, 0x2, 0x0, 0x0, 0x1f,
    0x31, 0xb0, 0x0, 0x0, 0xf2, 0x2f, 0x0, 0x0,
    0x8b, 0x0, 0x5a, 0x89, 0xa7, 0x0,

    /* U+0054 "T" */
    0x8c, 0x99, 0xcf, 0x99, 0x9f, 0x19, 0x50, 0x7,
    0xf0, 0x0, 0xc2, 0x92, 0x0, 0x7f, 0x0, 0x9,
    0x21, 0x0, 0x6, 0xf0, 0x0, 0x10, 0x0, 0x0,
    0x6f, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x0, 0x0, 0x0, 0x2, 0x8c, 0xf9,
    0x70, 0x0,

    /* U+0055 "U" */
    0x28, 0xee, 0x83, 0x0, 0x8a, 0xd8, 0x0, 0xb,
    0xb0, 0x0, 0x0, 0x18, 0x0, 0x0, 0xbb, 0x0,
    0x0, 0x1, 0x80, 0x0, 0xb, 0xb0, 0x0, 0x0,
    0x18, 0x0, 0x0, 0xbb, 0x0, 0x0, 0x1, 0x80,
    0x0, 0xb, 0xa0, 0x0, 0x0, 0x18, 0x0, 0x0,
    0xba, 0x0, 0x0, 0x1, 0x80, 0x0, 0xb, 0xb0,
    0x0, 0x0, 0x27, 0x0, 0x0, 0x9d, 0x0, 0x0,
    0x4, 0x50, 0x0, 0x5, 0xf2, 0x0, 0x0, 0x92,
    0x0, 0x0, 0xd, 0xc2, 0x0, 0x69, 0x0, 0x0,
    0x0, 0x19, 0xde, 0xd7, 0x0, 0x0,

    /* U+0056 "V" */
    0x7c, 0xf9, 0x60, 0x5, 0x9e, 0x92, 0x3, 0xf3,
    0x0, 0x0, 0x28, 0x0, 0x0, 0xe8, 0x0, 0x0,
    0x73, 0x0, 0x0, 0x8e, 0x0, 0x0, 0xa0, 0x0,
    0x0, 0x3f, 0x30, 0x1, 0x90, 0x0, 0x0, 0xd,
    0x80, 0x6, 0x30, 0x0, 0x0, 0x8, 0xd0, 0x9,
    0x0, 0x0, 0x0, 0x2, 0xf3, 0x8, 0x0, 0x0,
    0x0, 0x0, 0xd8, 0x54, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0x90, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xc, 0x40, 0x0, 0x0,

    /* U+0057 "W" */
    0x6b, 0xfa, 0x50, 0x39, 0xec, 0x80, 0x18, 0xda,
    0x60, 0x2f, 0x40, 0x0, 0xf, 0x90, 0x0, 0xa,
    0x0, 0x0, 0xd8, 0x0, 0x4, 0xbd, 0x0, 0x0,
    0x90, 0x0, 0x9, 0xc0, 0x0, 0x83, 0xf2, 0x0,
    0x45, 0x0, 0x0, 0x5f, 0x10, 0x9, 0xd, 0x70,
    0x8, 0x10, 0x0, 0x0, 0xf5, 0x2, 0x60, 0x9b,
    0x0, 0x90, 0x0, 0x0, 0xc, 0x90, 0x71, 0x4,
    0xf0, 0x8, 0x0, 0x0, 0x0, 0x7d, 0x9, 0x0,
    0xf, 0x44, 0x40, 0x0, 0x0, 0x3, 0xf2, 0x80,
    0x0, 0xb9, 0x80, 0x0, 0x0, 0x0, 0xe, 0xb3,
    0x0, 0x6, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xae,
    0x0, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0, 0x6,
    0xa0, 0x0, 0x0, 0xc3, 0x0, 0x0,

    /* U+0058 "X" */
    0x4a, 0xfc, 0x80, 0x19, 0xda, 0x50, 0xb, 0xd0,
    0x0, 0x19, 0x0, 0x0, 0x2f, 0x60, 0x9, 0x10,
    0x0, 0x0, 0x8e, 0x13, 0x50, 0x0, 0x0, 0x0,
    0xe9, 0x80, 0x0, 0x0, 0x0, 0x6, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xb0, 0x0, 0x0, 0x0,
    0x9, 0x3f, 0x40, 0x0, 0x0, 0x7, 0x20, 0xad,
    0x0, 0x0, 0x2, 0x80, 0x1, 0xf7, 0x0, 0x0,
    0xa0, 0x0, 0x8, 0xe1, 0x6, 0xad, 0x91, 0x5,
    0xaf, 0xc8,

    /* U+0059 "Y" */
    0x4b, 0xfb, 0x80, 0x8, 0xcb, 0x60, 0xe, 0x90,
    0x0, 0xa, 0x0, 0x0, 0x7f, 0x10, 0x3, 0x60,
    0x0, 0x1, 0xf8, 0x0, 0x90, 0x0, 0x0, 0x8,
    0xe0, 0x27, 0x0, 0x0, 0x0, 0x1f, 0x78, 0x0,
    0x0, 0x0, 0x0, 0x8e, 0x70, 0x0, 0x0, 0x0,
    0x3, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x30,
    0x0, 0x0, 0x0, 0x3, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x30, 0x0, 0x0, 0x0, 0x8b, 0xfb,
    0x80, 0x0,

    /* U+005A "Z" */
    0x3e, 0x99, 0x99, 0x9e, 0xd0, 0x4a, 0x0, 0x0,
    0x3f, 0x50, 0x47, 0x0, 0x0, 0xda, 0x0, 0x0,
    0x0, 0x7, 0xf1, 0x0, 0x0, 0x0, 0x2f, 0x60,
    0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0, 0x6,
    0xf2, 0x0, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x0,
    0x0, 0xad, 0x0, 0x0, 0x10, 0x5, 0xf3, 0x0,
    0x0, 0x93, 0x1e, 0x90, 0x0, 0x0, 0xc2, 0x8f,
    0xa9, 0x99, 0x99, 0xf1,

    /* U+005B "[" */
    0x4f, 0xa7, 0x4e, 0x0, 0x4d, 0x0, 0x3d, 0x0,
    0x3d, 0x0, 0x3d, 0x0, 0x3d, 0x0, 0x3d, 0x0,
    0x3d, 0x0, 0x3d, 0x0, 0x3d, 0x0, 0x3d, 0x0,
    0x3d, 0x0, 0x4d, 0x0, 0x4e, 0x0, 0x4f, 0xa7,

    /* U+005C "\\" */
    0xb0, 0x0, 0x0, 0x74, 0x0, 0x0, 0x29, 0x0,
    0x0, 0xb, 0x0, 0x0, 0x9, 0x20, 0x0, 0x4,
    0x70, 0x0, 0x0, 0xb0, 0x0, 0x0, 0xa0, 0x0,
    0x0, 0x65, 0x0, 0x0, 0x19, 0x0, 0x0, 0xb,
    0x0, 0x0, 0x8, 0x20, 0x0, 0x4, 0x70, 0x0,
    0x0, 0xb0, 0x0, 0x0, 0xa1, 0x0, 0x0, 0x65,

    /* U+005D "]" */
    0x39, 0xcc, 0x0, 0x6c, 0x0, 0x6c, 0x0, 0x5b,
    0x0, 0x5b, 0x0, 0x5b, 0x0, 0x5b, 0x0, 0x5b,
    0x0, 0x5b, 0x0, 0x5b, 0x0, 0x5b, 0x0, 0x5b,
    0x0, 0x5b, 0x0, 0x6c, 0x0, 0x6c, 0x39, 0xcc,

    /* U+005E "^" */
    0x0, 0x1e, 0x40, 0x0, 0x8, 0x4b, 0x0, 0x2,
    0xa0, 0x57, 0x0, 0xa1, 0x0, 0xb1, 0x47, 0x0,
    0x2, 0xa0, 0x0, 0x0, 0x1,

    /* U+005F "_" */
    0x1c, 0xcc, 0xcc, 0xcc, 0xcc, 0x10,

    /* U+0060 "`" */
    0x6a, 0x0, 0x1d, 0x60, 0x1, 0xb1, 0x0, 0x12,

    /* U+0061 "a" */
    0x0, 0x88, 0x9c, 0x30, 0x0, 0xa9, 0x0, 0x6d,
    0x0, 0x9, 0x30, 0x3, 0xf0, 0x0, 0x0, 0x1,
    0x7f, 0x10, 0x0, 0x69, 0x53, 0xf1, 0x0, 0xa9,
    0x0, 0x2f, 0x10, 0x1f, 0x30, 0x2, 0xf1, 0x0,
    0xf7, 0x1, 0x8f, 0x20, 0x6, 0xec, 0x80, 0x9c,
    0x50,

    /* U+0062 "b" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xf5,
    0x7b, 0xd9, 0x0, 0x0, 0xfa, 0x10, 0x1d, 0xa0,
    0x0, 0xf5, 0x0, 0x4, 0xf1, 0x0, 0xf5, 0x0,
    0x1, 0xf4, 0x0, 0xf5, 0x0, 0x0, 0xf6, 0x0,
    0xf5, 0x0, 0x1, 0xf4, 0x0, 0xf5, 0x0, 0x5,
    0xf1, 0x0, 0xfa, 0x10, 0x1d, 0x80, 0x3c, 0xf4,
    0x8c, 0xd8, 0x0,

    /* U+0063 "c" */
    0x0, 0x2a, 0x98, 0x80, 0x2, 0xe3, 0x0, 0xa9,
    0xc, 0x80, 0x0, 0x38, 0xf, 0x40, 0x0, 0x0,
    0x3f, 0x30, 0x0, 0x0, 0x1f, 0x40, 0x0, 0x0,
    0xd, 0xa0, 0x0, 0x0, 0x5, 0xf7, 0x0, 0x27,
    0x0, 0x4c, 0xeb, 0x80,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xbe, 0x20, 0x0, 0x0, 0x0, 0x1f, 0x20, 0x0,
    0x0, 0x0, 0x1f, 0x20, 0x0, 0x0, 0x0, 0x1f,
    0x20, 0x0, 0x0, 0x0, 0x1f, 0x20, 0x0, 0x4c,
    0xca, 0x4f, 0x20, 0x4, 0xf4, 0x0, 0x8f, 0x20,
    0xd, 0x90, 0x0, 0x2f, 0x20, 0x1f, 0x40, 0x0,
    0x2f, 0x20, 0x3f, 0x30, 0x0, 0x2f, 0x20, 0x1f,
    0x40, 0x0, 0x2f, 0x20, 0xe, 0x70, 0x0, 0x2f,
    0x20, 0x6, 0xe1, 0x0, 0x7f, 0x20, 0x0, 0x6c,
    0x98, 0x4f, 0xa6,

    /* U+0065 "e" */
    0x0, 0x3a, 0x89, 0x60, 0x0, 0x3e, 0x20, 0xb,
    0x60, 0xc, 0x80, 0x0, 0x5d, 0x1, 0xf4, 0x0,
    0x5, 0xf0, 0x3f, 0x98, 0x88, 0xbb, 0x1, 0xf4,
    0x0, 0x0, 0x0, 0xd, 0x80, 0x0, 0x0, 0x0,
    0x4f, 0x50, 0x1, 0x70, 0x0, 0x4c, 0xeb, 0x90,
    0x0,

    /* U+0066 "f" */
    0x0, 0x2, 0x8d, 0xc1, 0x0, 0xc, 0x12, 0x70,
    0x0, 0x79, 0x0, 0x0, 0x0, 0xc6, 0x0, 0x0,
    0x0, 0xe5, 0x0, 0x0, 0x39, 0xfc, 0x98, 0x0,
    0x0, 0xf5, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0,
    0x0, 0xf5, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0,
    0x0, 0xf6, 0x0, 0x0, 0x0, 0xf6, 0x0, 0x0,
    0x0, 0xf6, 0x0, 0x0, 0x39, 0xfc, 0x80, 0x0,

    /* U+0067 "g" */
    0x0, 0x99, 0x8a, 0x6a, 0x80, 0x9a, 0x0, 0x4e,
    0x21, 0xd, 0x50, 0x0, 0xf3, 0x0, 0xc5, 0x0,
    0xf, 0x30, 0x6, 0xc0, 0x7, 0xc0, 0x0, 0x9,
    0x88, 0x70, 0x0, 0x8, 0x30, 0x0, 0x0, 0x0,
    0xb9, 0x33, 0x31, 0x0, 0x3, 0xef, 0xff, 0xfc,
    0x10, 0x92, 0x0, 0x2, 0xe7, 0x3b, 0x0, 0x0,
    0xb, 0x63, 0xe1, 0x0, 0x4, 0xc0, 0x5, 0xb9,
    0x99, 0x70, 0x0,

    /* U+0068 "h" */
    0x2, 0x52, 0x0, 0x0, 0x0, 0x2, 0x7f, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x50, 0x0, 0x0, 0x0, 0x0, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x53, 0xbe, 0xb1,
    0x0, 0x0, 0xf9, 0x61, 0x1e, 0x80, 0x0, 0xf,
    0x60, 0x0, 0x9b, 0x0, 0x0, 0xf5, 0x0, 0x8,
    0xc0, 0x0, 0xf, 0x50, 0x0, 0x8c, 0x0, 0x0,
    0xf5, 0x0, 0x8, 0xc0, 0x0, 0xf, 0x50, 0x0,
    0x8c, 0x0, 0x0, 0xf6, 0x0, 0x8, 0xd0, 0x2,
    0x8f, 0xb4, 0x7, 0xce, 0x80,

    /* U+0069 "i" */
    0x2, 0xe4, 0x0, 0x1d, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x27, 0xb5, 0x1, 0x3f, 0x60, 0x0,
    0xf6, 0x0, 0xe, 0x50, 0x0, 0xe5, 0x0, 0xe,
    0x60, 0x0, 0xe6, 0x0, 0xf, 0x60, 0x28, 0xfb,
    0x50,

    /* U+006A "j" */
    0x0, 0x3, 0xe4, 0x0, 0x2, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xd5, 0x0,
    0x1, 0xf5, 0x0, 0x0, 0xf4, 0x0, 0x0, 0xf4,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0,
    0xf4, 0x0, 0x0, 0xf4, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0xf2, 0x0, 0x2, 0xf0, 0x6, 0x26, 0x90,
    0x1d, 0xd8, 0x0,

    /* U+006B "k" */
    0x2, 0x52, 0x0, 0x0, 0x0, 0x27, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xf5, 0x5, 0xbd, 0x92, 0x0, 0xf5,
    0x0, 0xa1, 0x0, 0x0, 0xf5, 0x9, 0x10, 0x0,
    0x0, 0xf5, 0xac, 0x0, 0x0, 0x0, 0xfc, 0x4f,
    0x50, 0x0, 0x0, 0xf6, 0x9, 0xd0, 0x0, 0x0,
    0xf5, 0x1, 0xf6, 0x0, 0x0, 0xf6, 0x0, 0x8e,
    0x0, 0x29, 0xfb, 0x50, 0xe, 0xb5,

    /* U+006C "l" */
    0x2, 0x52, 0x2, 0x7f, 0x60, 0x0, 0xf5, 0x0,
    0xf, 0x50, 0x0, 0xf5, 0x0, 0xf, 0x50, 0x0,
    0xf5, 0x0, 0xf, 0x50, 0x0, 0xf5, 0x0, 0xf,
    0x50, 0x0, 0xf5, 0x0, 0xf, 0x50, 0x0, 0xf6,
    0x3, 0x9f, 0xb6,

    /* U+006D "m" */
    0x28, 0xc3, 0x4b, 0xe9, 0x3, 0xbe, 0xb1, 0x0,
    0x13, 0xf9, 0x60, 0x2f, 0x87, 0x10, 0xd8, 0x0,
    0x0, 0xf7, 0x0, 0xc, 0x90, 0x0, 0x8b, 0x0,
    0x0, 0xf5, 0x0, 0xb, 0x80, 0x0, 0x7c, 0x0,
    0x0, 0xf5, 0x0, 0xb, 0x80, 0x0, 0x7c, 0x0,
    0x0, 0xf5, 0x0, 0xb, 0x90, 0x0, 0x7d, 0x0,
    0x0, 0xf5, 0x0, 0xb, 0x90, 0x0, 0x7d, 0x0,
    0x0, 0xf5, 0x0, 0xb, 0x90, 0x0, 0x8d, 0x0,
    0x28, 0xfb, 0x40, 0x8e, 0xd6, 0x6, 0xce, 0x80,

    /* U+006E "n" */
    0x28, 0xc3, 0x4b, 0xeb, 0x10, 0x1, 0x3f, 0x96,
    0x11, 0xe7, 0x0, 0x0, 0xf6, 0x0, 0x9, 0xb0,
    0x0, 0xf, 0x50, 0x0, 0x8c, 0x0, 0x0, 0xf5,
    0x0, 0x8, 0xc0, 0x0, 0xf, 0x50, 0x0, 0x8c,
    0x0, 0x0, 0xf5, 0x0, 0x8, 0xc0, 0x0, 0xf,
    0x50, 0x0, 0x8c, 0x0, 0x28, 0xfb, 0x40, 0x7d,
    0xe8, 0x0,

    /* U+006F "o" */
    0x0, 0x3a, 0x99, 0x80, 0x0, 0x3e, 0x10, 0x8,
    0xb0, 0xd, 0x70, 0x0, 0xf, 0x51, 0xf4, 0x0,
    0x0, 0xc9, 0x3f, 0x30, 0x0, 0xb, 0xb1, 0xf4,
    0x0, 0x0, 0xc9, 0xd, 0x70, 0x0, 0xf, 0x50,
    0x4e, 0x10, 0x7, 0xc0, 0x0, 0x3a, 0x89, 0x80,
    0x0,

    /* U+0070 "p" */
    0x28, 0xc4, 0x7b, 0xd9, 0x0, 0x12, 0xfa, 0x10,
    0x1d, 0x90, 0x0, 0xf5, 0x0, 0x4, 0xf1, 0x0,
    0xf5, 0x0, 0x1, 0xf4, 0x0, 0xf5, 0x0, 0x0,
    0xf6, 0x0, 0xf5, 0x0, 0x1, 0xf4, 0x0, 0xf5,
    0x0, 0x5, 0xf1, 0x0, 0xfa, 0x0, 0x1d, 0x80,
    0x0, 0xf6, 0x9c, 0xd7, 0x0, 0x0, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xf5, 0x0, 0x0, 0x0, 0x28, 0xfb, 0x70, 0x0,
    0x0,

    /* U+0071 "q" */
    0x0, 0x4c, 0xca, 0x25, 0x20, 0x4, 0xf4, 0x0,
    0x7f, 0x20, 0xd, 0x80, 0x0, 0x2f, 0x20, 0x1f,
    0x40, 0x0, 0x2f, 0x20, 0x3f, 0x30, 0x0, 0x2f,
    0x20, 0x1f, 0x40, 0x0, 0x2f, 0x20, 0xe, 0x80,
    0x0, 0x2f, 0x20, 0x6, 0xf3, 0x0, 0x7f, 0x20,
    0x0, 0x6d, 0xca, 0x3f, 0x20, 0x0, 0x0, 0x0,
    0x1f, 0x20, 0x0, 0x0, 0x0, 0x1f, 0x20, 0x0,
    0x0, 0x0, 0x2f, 0x20, 0x0, 0x0, 0x5, 0x9f,
    0xa4,

    /* U+0072 "r" */
    0x28, 0xc3, 0x4d, 0xd1, 0x13, 0xf7, 0x93, 0xa1,
    0x0, 0xfc, 0x0, 0x0, 0x0, 0xf6, 0x0, 0x0,
    0x0, 0xf5, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0,
    0x0, 0xf5, 0x0, 0x0, 0x0, 0xf5, 0x0, 0x0,
    0x29, 0xfc, 0x70, 0x0,

    /* U+0073 "s" */
    0x1, 0xaa, 0x9a, 0x20, 0xc6, 0x0, 0x76, 0xf,
    0x50, 0x2, 0x30, 0xbe, 0x71, 0x0, 0x0, 0x8f,
    0xf9, 0x0, 0x0, 0x6, 0xf9, 0x16, 0x0, 0x8,
    0xc2, 0xc0, 0x0, 0x98, 0x9, 0x98, 0x98, 0x0,

    /* U+0074 "t" */
    0x0, 0x51, 0x0, 0x0, 0xd2, 0x0, 0x0, 0xf2,
    0x0, 0x6a, 0xfa, 0x94, 0x2, 0xf2, 0x0, 0x2,
    0xf2, 0x0, 0x2, 0xf2, 0x0, 0x2, 0xf2, 0x0,
    0x2, 0xf2, 0x0, 0x2, 0xf2, 0x0, 0x1, 0xf5,
    0x0, 0x0, 0x8e, 0xb4,

    /* U+0075 "u" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3b, 0xf2, 0x0,
    0x9e, 0x90, 0x2, 0xf2, 0x0, 0xb, 0x90, 0x2,
    0xf2, 0x0, 0xb, 0x80, 0x2, 0xf2, 0x0, 0xb,
    0x80, 0x2, 0xf2, 0x0, 0xb, 0x80, 0x2, 0xf2,
    0x0, 0xb, 0x80, 0x1, 0xf3, 0x0, 0xd, 0x80,
    0x0, 0xea, 0x2, 0x8d, 0x80, 0x0, 0x4e, 0xe9,
    0x1b, 0xe8,

    /* U+0076 "v" */
    0x19, 0xfc, 0x70, 0x3a, 0xd6, 0x0, 0xbb, 0x0,
    0x4, 0x40, 0x0, 0x5f, 0x10, 0x8, 0x0, 0x0,
    0xf, 0x60, 0x8, 0x0, 0x0, 0xa, 0xb0, 0x43,
    0x0, 0x0, 0x4, 0xf1, 0x80, 0x0, 0x0, 0x0,
    0xe7, 0x70, 0x0, 0x0, 0x0, 0x8e, 0x30, 0x0,
    0x0, 0x0, 0x2d, 0x0, 0x0,

    /* U+0077 "w" */
    0x9, 0xfc, 0x60, 0x6d, 0xd7, 0x7, 0xca, 0x20,
    0xb, 0x90, 0x0, 0xbc, 0x0, 0x9, 0x0, 0x0,
    0x6e, 0x0, 0x17, 0xf2, 0x0, 0x80, 0x0, 0x1,
    0xf3, 0x6, 0x1b, 0x70, 0x35, 0x0, 0x0, 0xc,
    0x80, 0x70, 0x6c, 0x7, 0x0, 0x0, 0x0, 0x7c,
    0x16, 0x1, 0xf1, 0x70, 0x0, 0x0, 0x2, 0xf7,
    0x10, 0xc, 0x86, 0x0, 0x0, 0x0, 0xc, 0xb0,
    0x0, 0x6f, 0x10, 0x0, 0x0, 0x0, 0x76, 0x0,
    0x1, 0xc0, 0x0, 0x0,

    /* U+0078 "x" */
    0x6c, 0xf9, 0x3, 0xbc, 0x50, 0x1f, 0x60, 0x9,
    0x10, 0x0, 0x7f, 0x15, 0x40, 0x0, 0x0, 0xcb,
    0x80, 0x0, 0x0, 0x4, 0xf5, 0x0, 0x0, 0x0,
    0x89, 0xe0, 0x0, 0x0, 0x54, 0xe, 0x90, 0x0,
    0x18, 0x0, 0x5f, 0x30, 0x7d, 0xa2, 0x8, 0xfd,
    0x70,

    /* U+0079 "y" */
    0x8, 0xfc, 0x60, 0x38, 0xd6, 0x0, 0xbb, 0x0,
    0x2, 0x60, 0x0, 0x5f, 0x10, 0x7, 0x10, 0x0,
    0xe, 0x60, 0x8, 0x0, 0x0, 0x8, 0xc0, 0x35,
    0x0, 0x0, 0x2, 0xf2, 0x70, 0x0, 0x0, 0x0,
    0xc8, 0x70, 0x0, 0x0, 0x0, 0x5f, 0x30, 0x0,
    0x0, 0x0, 0xd, 0x0, 0x0, 0x0, 0x0, 0x7,
    0x0, 0x0, 0x0, 0x0, 0x71, 0x0, 0x0, 0x8,
    0x43, 0x70, 0x0, 0x0, 0xb, 0xe8, 0x0, 0x0,
    0x0,

    /* U+007A "z" */
    0x2e, 0x88, 0x8b, 0xf2, 0x39, 0x0, 0xd, 0x80,
    0x24, 0x0, 0x7e, 0x0, 0x0, 0x2, 0xf4, 0x0,
    0x0, 0xc, 0xa0, 0x0, 0x0, 0x6e, 0x10, 0x0,
    0x1, 0xe6, 0x0, 0x36, 0xa, 0xc0, 0x0, 0x76,
    0x3f, 0xa8, 0x88, 0xd5,

    /* U+007B "{" */
    0x0, 0x3b, 0x94, 0x0, 0xd5, 0x0, 0x0, 0xf1,
    0x0, 0x0, 0xc3, 0x0, 0x0, 0x86, 0x0, 0x0,
    0x49, 0x0, 0x0, 0x56, 0x0, 0x18, 0x80, 0x0,
    0x5, 0xa1, 0x0, 0x0, 0x47, 0x0, 0x0, 0x58,
    0x0, 0x0, 0x95, 0x0, 0x0, 0xd3, 0x0, 0x0,
    0xf1, 0x0, 0x0, 0xc6, 0x0, 0x0, 0x19, 0x94,

    /* U+007C "|" */
    0x6c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xc0,

    /* U+007D "}" */
    0x49, 0xb3, 0x0, 0x0, 0x5d, 0x0, 0x0, 0x2f,
    0x0, 0x0, 0x4c, 0x0, 0x0, 0x68, 0x0, 0x0,
    0x94, 0x0, 0x0, 0x65, 0x0, 0x0, 0x8, 0x81,
    0x0, 0x1a, 0x50, 0x0, 0x74, 0x0, 0x0, 0x95,
    0x0, 0x0, 0x69, 0x0, 0x0, 0x3c, 0x0, 0x0,
    0x2f, 0x0, 0x0, 0x7c, 0x0, 0x49, 0x91, 0x0,

    /* U+007E "~" */
    0x3, 0xbb, 0x20, 0x3, 0x40, 0xb1, 0x1b, 0x40,
    0x92, 0x7, 0x0, 0x9, 0xb6, 0x0,

    /* U+503C "值" */
    0x0, 0x0, 0x61, 0x0, 0x4, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0x20, 0x0, 0x99, 0x0, 0x2,
    0x0, 0x0, 0x8, 0xb6, 0x77, 0x7c, 0xb7, 0x7a,
    0xf3, 0x0, 0x0, 0xe5, 0x0, 0x0, 0xa6, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0x0, 0x40, 0xa, 0x40,
    0x18, 0x0, 0x0, 0xa, 0xe0, 0xb, 0xa7, 0x77,
    0x79, 0xf3, 0x0, 0x1, 0xed, 0x0, 0xb4, 0x0,
    0x0, 0x2d, 0x0, 0x0, 0x84, 0xd0, 0xb, 0x97,
    0x77, 0x78, 0xd0, 0x0, 0x13, 0x3d, 0x0, 0xb4,
    0x0, 0x0, 0x2d, 0x0, 0x0, 0x3, 0xd0, 0xb,
    0x97, 0x77, 0x78, 0xd0, 0x0, 0x0, 0x3d, 0x0,
    0xb4, 0x0, 0x0, 0x2d, 0x0, 0x0, 0x3, 0xd0,
    0xb, 0xa7, 0x77, 0x79, 0xd0, 0x0, 0x0, 0x3d,
    0x0, 0xb4, 0x0, 0x0, 0x2d, 0x0, 0x0, 0x3,
    0xd0, 0xb, 0x40, 0x0, 0x2, 0xd5, 0x20, 0x0,
    0x3b, 0x37, 0x98, 0x77, 0x77, 0x79, 0x97, 0x0,

    /* U+540D "名" */
    0x0, 0x0, 0x0, 0x53, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xe7, 0x77, 0x78, 0xe3, 0x0, 0x0,
    0x5, 0xc0, 0x0, 0x0, 0xbb, 0x10, 0x0, 0x2,
    0xc9, 0x10, 0x0, 0x8e, 0x10, 0x0, 0x1, 0xb1,
    0x3e, 0x20, 0x5e, 0x30, 0x0, 0x2, 0x81, 0x0,
    0x98, 0x6e, 0x30, 0x0, 0x0, 0x40, 0x0, 0x0,
    0x9b, 0x10, 0x0, 0x0, 0x0, 0x0, 0x3, 0xc6,
    0x0, 0x0, 0x3, 0x0, 0x0, 0x19, 0xfa, 0x77,
    0x77, 0x78, 0xf4, 0x4, 0x76, 0x1e, 0x10, 0x0,
    0x0, 0x1f, 0x2, 0x20, 0x0, 0xe1, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0xe, 0x10, 0x0, 0x0,
    0x1f, 0x0, 0x0, 0x0, 0xe8, 0x77, 0x77, 0x78,
    0xf0, 0x0, 0x0, 0xd, 0x10, 0x0, 0x0, 0x1c,
    0x0,

    /* U+5B57 "字" */
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x40, 0x0, 0x1, 0xd0, 0x0, 0x4, 0x10,
    0x1, 0xc7, 0x77, 0x77, 0x77, 0x77, 0x7f, 0xb0,
    0x1c, 0x80, 0x0, 0x0, 0x0, 0x1, 0x3b, 0x0,
    0x3b, 0x15, 0x77, 0x77, 0x77, 0xbd, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xc2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x84, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x40, 0x0, 0x4, 0x0,
    0x37, 0x77, 0x77, 0x7e, 0xa7, 0x77, 0x8e, 0xa0,
    0x0, 0x0, 0x0, 0xc, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x11, 0xc, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x14, 0xef, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+6210 "成" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd5, 0x49, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd3, 0x3, 0xe0, 0x0,
    0x0, 0x40, 0x0, 0x0, 0xc3, 0x0, 0xa, 0x30,
    0x0, 0xd9, 0x77, 0x77, 0xd9, 0x77, 0x77, 0x50,
    0x0, 0xd3, 0x0, 0x0, 0x96, 0x0, 0x0, 0x0,
    0x0, 0xd3, 0x0, 0x50, 0x68, 0x0, 0xd7, 0x0,
    0x0, 0xd9, 0x77, 0xf4, 0x4b, 0x3, 0xe0, 0x0,
    0x0, 0xe2, 0x0, 0xe1, 0xf, 0x9, 0x80, 0x0,
    0x0, 0xf1, 0x0, 0xf0, 0xa, 0x8e, 0x10, 0x0,
    0x0, 0xf0, 0x1, 0xe0, 0x4, 0xf6, 0x0, 0x0,
    0x2, 0xc0, 0x16, 0xb0, 0x7, 0xf6, 0x0, 0x20,
    0x6, 0x61, 0x8e, 0x50, 0x5a, 0x3e, 0x40, 0x70,
    0xa, 0x0, 0x0, 0x5, 0x80, 0x4, 0xf9, 0x90,
    0x34, 0x0, 0x1, 0x63, 0x0, 0x0, 0x2b, 0xe0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6C34 "水" */
    0x0, 0x0, 0x0, 0x4, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x80, 0x0, 0x3, 0x0,
    0x0, 0x0, 0x4, 0xc, 0x90, 0x0, 0x8e, 0x20,
    0x27, 0x77, 0x9f, 0x3c, 0x72, 0x4, 0xd2, 0x0,
    0x0, 0x0, 0x7a, 0xc, 0x48, 0x3a, 0x10, 0x0,
    0x0, 0x0, 0xb5, 0xc, 0x48, 0x70, 0x0, 0x0,
    0x0, 0x2, 0xe0, 0xc, 0x41, 0xb0, 0x0, 0x0,
    0x0, 0x9, 0x70, 0xc, 0x40, 0x88, 0x0, 0x0,
    0x0, 0x2c, 0x0, 0xc, 0x40, 0xd, 0x70, 0x0,
    0x0, 0xb2, 0x0, 0xc, 0x40, 0x2, 0xea, 0x10,
    0x8, 0x30, 0x0, 0xc, 0x40, 0x0, 0x3e, 0xe1,
    0x41, 0x0, 0x12, 0x1c, 0x40, 0x0, 0x1, 0x50,
    0x0, 0x0, 0x3, 0xef, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,

    /* U+6D47 "浇" */
    0x0, 0x10, 0x0, 0x1, 0x61, 0x0, 0x0, 0x0,
    0x0, 0x7c, 0x20, 0x2, 0xe0, 0x0, 0x1, 0x0,
    0x0, 0x9, 0xa0, 0x0, 0xf1, 0x0, 0x4f, 0x70,
    0x0, 0x0, 0x3, 0x46, 0xeb, 0x76, 0x74, 0x0,
    0x17, 0x20, 0x42, 0x30, 0x4e, 0x23, 0xe8, 0x0,
    0x3, 0xf2, 0x70, 0x0, 0x9, 0xed, 0x40, 0x1,
    0x0, 0x44, 0x50, 0x1, 0x8c, 0x9e, 0x72, 0x61,
    0x0, 0x9, 0x4, 0x67, 0x20, 0x2, 0x8e, 0xf4,
    0x0, 0x1a, 0x0, 0x0, 0x0, 0x0, 0x6, 0x50,
    0x0, 0x84, 0x7, 0x78, 0xc7, 0x8c, 0x79, 0x80,
    0x3, 0xe1, 0x0, 0x6, 0xa0, 0x2d, 0x0, 0x0,
    0x6, 0xf0, 0x0, 0x9, 0x60, 0x2d, 0x0, 0x0,
    0x0, 0xf2, 0x0, 0x1d, 0x0, 0x2d, 0x0, 0x23,
    0x0, 0xf4, 0x0, 0xa3, 0x0, 0x2d, 0x0, 0x53,
    0x0, 0xd2, 0x26, 0x10, 0x0, 0xe, 0xee, 0xe7,

    /* U+7B49 "等" */
    0x0, 0x3, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x5d, 0x10, 0x20, 0x7c, 0x10, 0x3, 0x0,
    0x0, 0xda, 0x76, 0xc8, 0xd9, 0x87, 0x7d, 0x80,
    0x8, 0x70, 0xb1, 0x8, 0x50, 0x79, 0x0, 0x0,
    0x36, 0x0, 0x74, 0xd, 0x50, 0xb, 0x10, 0x0,
    0x0, 0x67, 0x77, 0x7d, 0x97, 0x77, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0xb, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0x40, 0x0, 0x8, 0x40,
    0x37, 0x77, 0x77, 0x77, 0x77, 0xd9, 0x77, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf1, 0x7, 0x0,
    0x6, 0x77, 0x77, 0x77, 0x77, 0xf8, 0x89, 0x50,
    0x0, 0x0, 0x69, 0x0, 0x0, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xa0, 0x0, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x91, 0x10, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x6f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,

    /* U+7EA7 "级" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x90, 0x77, 0x77, 0x77, 0xa9, 0x0,
    0x0, 0x4d, 0x0, 0x4, 0xc0, 0x0, 0xb6, 0x0,
    0x0, 0xc2, 0x6, 0x14, 0xc0, 0x1, 0xe0, 0x0,
    0x8, 0x40, 0x3f, 0x54, 0xd0, 0x8, 0x80, 0x0,
    0x3e, 0xb9, 0xe7, 0x4, 0xf1, 0xe, 0x21, 0x30,
    0x7, 0x28, 0xa0, 0x6, 0xd5, 0x4b, 0x7c, 0xd0,
    0x0, 0x4a, 0x0, 0x7, 0x88, 0x0, 0xc, 0x50,
    0x3, 0xa0, 0x3, 0x19, 0x69, 0x10, 0x1e, 0x0,
    0x1f, 0xcc, 0x83, 0xb, 0x35, 0x90, 0x89, 0x0,
    0x7, 0x20, 0x0, 0xe, 0x0, 0xd4, 0xd0, 0x0,
    0x0, 0x0, 0x35, 0x59, 0x0, 0x5f, 0x50, 0x0,
    0x5, 0x9c, 0x60, 0xa1, 0x0, 0xad, 0xc1, 0x0,
    0x1e, 0x70, 0x4, 0x70, 0x1a, 0x40, 0xbe, 0x60,
    0x0, 0x0, 0x17, 0x6, 0x71, 0x0, 0x7, 0xe4,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+957F "长" */
    0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf2, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x9e, 0x50, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x2c, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x8, 0xb3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf5, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf2, 0x0, 0x0, 0x0, 0x7, 0x40,
    0x7, 0x77, 0xf7, 0x79, 0x77, 0x77, 0x79, 0x90,
    0x0, 0x0, 0xf0, 0x2, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x2b, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x5, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x2, 0x60, 0x6e, 0x60, 0x0,
    0x0, 0x4, 0xf9, 0xc7, 0x0, 0x4, 0xee, 0x81,
    0x0, 0x5, 0xe7, 0x0, 0x0, 0x0, 0x6, 0x70,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdc,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xbf, 0xff, 0xff,
    0x0, 0x0, 0x3, 0x8d, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xc7, 0xff,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x51, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x84, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x2b, 0xee, 0xff,
    0x0, 0x0, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0x2b, 0xee, 0xff, 0x0, 0x0, 0xdf, 0xff, 0xfd,
    0xdf, 0xff, 0xff, 0x0, 0x0, 0x2b, 0xff, 0xb2,
    0xdf, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xf0, 0xf, 0xec, 0xcc, 0xcc, 0xce, 0xf0, 0xf,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xf0, 0xf, 0x80, 0x0, 0x0, 0x8, 0xf0, 0xf,
    0xff, 0xff, 0xc8, 0x88, 0x88, 0x8c, 0xff, 0xff,
    0xd0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,

    /* U+F00B "" */
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0x73, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xb1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xc0,
    0x1b, 0xa0, 0x0, 0x0, 0xb, 0xff, 0xfc, 0x0,
    0xcf, 0xfb, 0x0, 0x0, 0xbf, 0xff, 0xc0, 0x0,
    0xbf, 0xff, 0xb0, 0xb, 0xff, 0xfc, 0x0, 0x0,
    0xc, 0xff, 0xfb, 0xbf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x3, 0x0, 0x0, 0x0, 0x3, 0x8, 0xfc, 0x10,
    0x0, 0x1c, 0xf8, 0xff, 0xfc, 0x10, 0x1c, 0xff,
    0xf5, 0xff, 0xfc, 0x2c, 0xff, 0xf5, 0x5, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x5, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x1d, 0xff, 0xfd, 0x10, 0x0, 0x1c,
    0xff, 0xff, 0xfc, 0x10, 0x1c, 0xff, 0xf9, 0xff,
    0xfc, 0x1c, 0xff, 0xf5, 0x5, 0xff, 0xfc, 0xdf,
    0xf5, 0x0, 0x5, 0xff, 0xd1, 0xa4, 0x0, 0x0,
    0x4, 0xa1,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x4f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x10, 0x6f, 0xf1, 0x3, 0x10, 0x0,
    0x0, 0x5f, 0xd0, 0x6f, 0xf1, 0x3f, 0xd1, 0x0,
    0x3, 0xff, 0xf1, 0x6f, 0xf1, 0x5f, 0xfd, 0x0,
    0xd, 0xff, 0x40, 0x6f, 0xf1, 0x9, 0xff, 0x70,
    0x4f, 0xf7, 0x0, 0x6f, 0xf1, 0x0, 0xcf, 0xe0,
    0x9f, 0xf0, 0x0, 0x6f, 0xf1, 0x0, 0x5f, 0xf3,
    0xbf, 0xc0, 0x0, 0x6f, 0xf1, 0x0, 0x2f, 0xf5,
    0xbf, 0xc0, 0x0, 0x4f, 0xe0, 0x0, 0x1f, 0xf6,
    0xaf, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0x6f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf0,
    0xf, 0xfe, 0x10, 0x0, 0x0, 0x5, 0xff, 0xa0,
    0x6, 0xff, 0xd3, 0x0, 0x0, 0x7f, 0xff, 0x20,
    0x0, 0x9f, 0xff, 0xda, 0xbe, 0xff, 0xf4, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x0, 0x0, 0x17, 0xbd, 0xca, 0x50, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x4, 0xfd, 0xdf, 0xff, 0xff, 0xfd, 0xef, 0x40,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x0, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x8, 0xff, 0xff, 0x20, 0x2, 0xff, 0xff, 0x80,
    0x4f, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff, 0xf4,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x4, 0xfe, 0xdf, 0xff, 0xff, 0xfd, 0xdf, 0x40,
    0x0, 0x30, 0x6, 0xff, 0xff, 0x60, 0x3, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8b, 0xb8, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x3, 0xdd, 0x30, 0x3f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5, 0x4f,
    0xf4, 0x0, 0x0, 0x0, 0x9, 0xff, 0x99, 0xff,
    0xbf, 0xf4, 0x0, 0x0, 0x1, 0xbf, 0xf6, 0x22,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x2d, 0xfe, 0x35,
    0xff, 0x53, 0xef, 0xf4, 0x0, 0x4, 0xff, 0xc1,
    0x8f, 0xff, 0xf8, 0x1c, 0xfe, 0x40, 0x7f, 0xfa,
    0x1a, 0xff, 0xff, 0xff, 0xa1, 0xaf, 0xf7, 0xcf,
    0x82, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x28, 0xfc,
    0x14, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x41, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xf, 0xff, 0xf9, 0x0, 0x8f,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8, 0x0,
    0x8f, 0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xf8,
    0x0, 0x8f, 0xff, 0xf0, 0x0, 0x0, 0xe, 0xff,
    0xf6, 0x0, 0x6f, 0xff, 0xe0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xfc, 0x1b, 0xb1, 0xcf, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xc2, 0x2c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F01C "" */
    0x0, 0x4, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0,
    0xb, 0xfa, 0x0, 0x5, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x50, 0x1e, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe1, 0xaf, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xfa, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F021 "" */
    0x0, 0x0, 0x6, 0xbd, 0xda, 0x50, 0x2, 0xff,
    0x0, 0x5, 0xef, 0xff, 0xff, 0xfe, 0x42, 0xff,
    0x0, 0x7f, 0xff, 0xa7, 0x7b, 0xff, 0xf9, 0xff,
    0x5, 0xff, 0xc1, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xe, 0xfc, 0x0, 0x0, 0x2, 0x22, 0xdf, 0xff,
    0x5f, 0xf2, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x8f, 0xb0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xb, 0xf8,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x2f, 0xf4,
    0xff, 0xfd, 0x22, 0x20, 0x0, 0x0, 0xcf, 0xe0,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x2c, 0xff, 0x40,
    0xff, 0x9f, 0xff, 0xb7, 0x6a, 0xff, 0xf7, 0x0,
    0xff, 0x24, 0xdf, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xff, 0x20, 0x5, 0xac, 0xdb, 0x60, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8d,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0x8f, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x0, 0x9, 0xff,
    0x0, 0x0, 0x0, 0x8d, 0x0, 0x0, 0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8d, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0x1, 0x50, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xbe, 0xff, 0xff, 0xff, 0xff, 0x0, 0xae,
    0xff, 0xff, 0xff, 0xff, 0x5, 0xf8, 0xdf, 0xff,
    0xff, 0xff, 0x2, 0x60, 0x0, 0x0, 0x9f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x3, 0xee, 0x10, 0x0, 0x0, 0x8, 0xff, 0x0,
    0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x8f, 0xff,
    0x0, 0x5, 0xfc, 0x7, 0xf4, 0xdf, 0xff, 0xff,
    0xff, 0x2, 0x50, 0x5f, 0x60, 0xf9, 0xff, 0xff,
    0xff, 0xff, 0x6, 0xf7, 0xd, 0xc0, 0xbd, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xf0, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xae, 0x9, 0xe0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x6, 0xf7, 0xd,
    0xc0, 0xad, 0xdf, 0xff, 0xff, 0xff, 0x2, 0x50,
    0x5f, 0x60, 0xe9, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x5, 0xfc, 0x6, 0xf4, 0x0, 0x0, 0x9, 0xff,
    0x0, 0xa, 0xb1, 0x2f, 0xb0, 0x0, 0x0, 0x0,
    0x8d, 0x0, 0x0, 0x2, 0xee, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,

    /* U+F03E "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0xc, 0xff, 0xff, 0xee, 0xff, 0xff,
    0xff, 0x20, 0x2f, 0xff, 0xfe, 0x12, 0xef, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xe1, 0x0, 0x2e, 0xff,
    0xff, 0xfe, 0x4e, 0xfe, 0x10, 0x0, 0x2, 0xff,
    0xff, 0xe1, 0x2, 0xc1, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F043 "" */
    0x0, 0x0, 0x4e, 0x40, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0x30, 0x0, 0xc, 0xff, 0xff, 0xfc,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xf8, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x9e, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0x2f, 0xff, 0xff, 0xff, 0xfe, 0xf2, 0xbf, 0xff,
    0xff, 0xfe, 0x9f, 0xa1, 0xbf, 0xff, 0xff, 0x92,
    0xff, 0xa2, 0x2f, 0xff, 0xf2, 0x4, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x2, 0x9e, 0xfe, 0x92, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x30, 0x0,
    0x1, 0xcc, 0xff, 0x40, 0x0, 0x2d, 0xff, 0xff,
    0x40, 0x3, 0xef, 0xff, 0xff, 0x40, 0x3f, 0xff,
    0xff, 0xff, 0x44, 0xff, 0xff, 0xff, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x45, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x4f, 0xff, 0xff, 0xff, 0x40, 0x3, 0xef,
    0xff, 0xff, 0x40, 0x0, 0x2d, 0xff, 0xff, 0x30,
    0x0, 0x1, 0xcc, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa,
    0x10, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd5,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb2, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfa, 0x20,
    0x0, 0x0, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x8e, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf8, 0x0, 0x8f, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xff,
    0xff, 0xff, 0x7f, 0xff, 0xf7, 0x0, 0x7f, 0xff,
    0xf7,

    /* U+F04D "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcc, 0x10, 0x0,
    0x3, 0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xff,
    0xfe, 0x30, 0x4, 0xff, 0xff, 0xff, 0xf4, 0x4,
    0xff, 0xff, 0xff, 0xff, 0x54, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0x44, 0xff, 0xff,
    0xff, 0xf3, 0x4, 0xff, 0xff, 0xfe, 0x30, 0x4,
    0xff, 0xff, 0xd2, 0x0, 0x4, 0xff, 0xcc, 0x10,
    0x0, 0x3, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x1a, 0x40, 0x0, 0x0, 0x1,
    0xdf, 0xf0, 0x0, 0x0, 0x1d, 0xff, 0xa0, 0x0,
    0x1, 0xdf, 0xfa, 0x0, 0x0, 0x1d, 0xff, 0xa0,
    0x0, 0x1, 0xdf, 0xfa, 0x0, 0x0, 0xc, 0xff,
    0xa0, 0x0, 0x0, 0xd, 0xff, 0x80, 0x0, 0x0,
    0x1, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0x80, 0x0, 0x0, 0x1, 0xdf, 0xf8, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x80, 0x0, 0x0, 0x1, 0xdf,
    0xf0, 0x0, 0x0, 0x0, 0x1b, 0x50,

    /* U+F054 "" */
    0x4, 0xa1, 0x0, 0x0, 0x0, 0xf, 0xfc, 0x10,
    0x0, 0x0, 0xa, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0xaf, 0xfc, 0x10, 0x0, 0x0, 0xa, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0xaf, 0xfc, 0x10, 0x0, 0x0,
    0xa, 0xff, 0xc0, 0x0, 0x0, 0x8, 0xff, 0xd0,
    0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8, 0xff,
    0xd1, 0x0, 0x0, 0x8f, 0xfd, 0x10, 0x0, 0x8,
    0xff, 0xd1, 0x0, 0x0, 0xf, 0xfd, 0x10, 0x0,
    0x0, 0x5, 0xb1, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x48, 0x88, 0x8c, 0xff, 0xc8,
    0x88, 0x84, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x48, 0x88, 0x8c, 0xff, 0xc8, 0x88, 0x84, 0x0,
    0x0, 0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x40,
    0x0, 0x0,

    /* U+F068 "" */
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x41, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xb7,

    /* U+F06E "" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf, 0xfd,
    0x40, 0x0, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4,
    0xef, 0xf7, 0x0, 0x7, 0xff, 0xf4, 0x0, 0x9e,
    0x80, 0x4f, 0xff, 0x70, 0x4f, 0xff, 0xc0, 0x0,
    0xaf, 0xf8, 0xc, 0xff, 0xf4, 0xdf, 0xff, 0x80,
    0x9a, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0xdf, 0xff,
    0x80, 0xef, 0xff, 0xfe, 0x8, 0xff, 0xfd, 0x4f,
    0xff, 0xc0, 0x8f, 0xff, 0xf8, 0xc, 0xff, 0xf4,
    0x7, 0xff, 0xf4, 0x8, 0xee, 0x80, 0x4f, 0xff,
    0x70, 0x0, 0x7f, 0xfe, 0x40, 0x0, 0x4, 0xef,
    0xf8, 0x0, 0x0, 0x4, 0xdf, 0xfc, 0x88, 0xcf,
    0xfd, 0x40, 0x0, 0x0, 0x0, 0x5, 0xad, 0xff,
    0xda, 0x50, 0x0, 0x0,

    /* U+F070 "" */
    0x8c, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0x80, 0x49,
    0xdf, 0xfd, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xd8, 0x8c, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x4, 0xef, 0xf8, 0x0, 0x0, 0x4e, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x1c, 0xff, 0x69, 0xe8,
    0x4, 0xff, 0xf7, 0x0, 0x4, 0xe3, 0x0, 0x9f,
    0xfe, 0xff, 0x80, 0xcf, 0xff, 0x40, 0xd, 0xff,
    0x70, 0x5, 0xff, 0xff, 0xe0, 0x8f, 0xff, 0xd0,
    0xd, 0xff, 0xf7, 0x0, 0x2d, 0xff, 0xe0, 0x8f,
    0xff, 0xd0, 0x4, 0xff, 0xfc, 0x0, 0x0, 0xaf,
    0xf8, 0xcf, 0xff, 0x30, 0x0, 0x7f, 0xff, 0x40,
    0x0, 0x6, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x8,
    0xff, 0xf4, 0x0, 0x0, 0x3e, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xc8, 0x82, 0x1, 0xbf,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5a, 0xdf, 0xfc,
    0x10, 0x8, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xc8,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xd2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xd8, 0x8d,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0xa, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xb0, 0xb, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xc0, 0xc, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xd0, 0xd,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xf9, 0x9f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xe2, 0x2e, 0xff, 0xff, 0xf8, 0x0,
    0x2, 0xff, 0xff, 0xff, 0x90, 0x9, 0xff, 0xff,
    0xff, 0x10, 0xa, 0xff, 0xff, 0xff, 0xe3, 0x3e,
    0xff, 0xff, 0xff, 0xa0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0x78, 0x8e, 0xff, 0x15, 0xff, 0xe8, 0xff, 0xe2,
    0x0, 0x2, 0xe5, 0x4f, 0xfe, 0x20, 0xfe, 0x20,
    0x0, 0x0, 0x13, 0xff, 0xf3, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0x31, 0x0, 0x52, 0x0,
    0x0, 0x2, 0xef, 0xf4, 0x5e, 0x20, 0xfe, 0x20,
    0x78, 0x8e, 0xff, 0x51, 0xff, 0xe8, 0xff, 0xe2,
    0xff, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0x70, 0x0, 0x7, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0x99,
    0xff, 0xd1, 0x0, 0x1, 0xdf, 0xf9, 0x0, 0x9f,
    0xfd, 0x10, 0x1d, 0xff, 0x90, 0x0, 0x9, 0xff,
    0xd1, 0xbf, 0xf9, 0x0, 0x0, 0x0, 0x9f, 0xfb,
    0x5f, 0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F078 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x90, 0x0, 0x0, 0x0, 0x9, 0xf5, 0xbf, 0xf9,
    0x0, 0x0, 0x0, 0x9f, 0xfb, 0x1d, 0xff, 0x90,
    0x0, 0x9, 0xff, 0xd1, 0x1, 0xdf, 0xf9, 0x0,
    0x9f, 0xfd, 0x10, 0x0, 0x1d, 0xff, 0x99, 0xff,
    0xd1, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xfd, 0x10,
    0xef, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x1d, 0xff,
    0xff, 0xd1, 0xaf, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x6b, 0x1f, 0xf1, 0xb6, 0x0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x6b, 0x1f,
    0xf1, 0xb6, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0,
    0xcf, 0xcf, 0xfc, 0xfc, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfa, 0x1d, 0xff, 0xff, 0xd1, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xfe, 0x1, 0xdf, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x8f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf0, 0xdf, 0xfd, 0xf, 0xff, 0xfd,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe0, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xea,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0x30, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x4f, 0xff, 0x90, 0x0, 0x2, 0x8f,
    0xf3, 0x0, 0x6f, 0xff, 0xd0, 0x0, 0xa, 0xff,
    0xff, 0xe4, 0xbf, 0xff, 0xd1, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xfb, 0x30, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xdb, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x8, 0xee, 0x80, 0x0, 0x0, 0x6, 0x61, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x2d, 0xff, 0xd0, 0xef,
    0x33, 0xfe, 0x0, 0x2e, 0xff, 0xf3, 0xe, 0xf3,
    0x3f, 0xe0, 0x2e, 0xff, 0xf3, 0x0, 0x8f, 0xff,
    0xff, 0x6e, 0xff, 0xf3, 0x0, 0x0, 0x8e, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x8, 0xef, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x8, 0xff, 0xff, 0xf6, 0xef,
    0xff, 0x30, 0x0, 0xef, 0x33, 0xfe, 0x2, 0xef,
    0xff, 0x30, 0xe, 0xf3, 0x3f, 0xe0, 0x2, 0xef,
    0xff, 0x30, 0x8f, 0xff, 0xf8, 0x0, 0x2, 0xdf,
    0xfd, 0x0, 0x8e, 0xe8, 0x0, 0x0, 0x0, 0x66,
    0x10,

    /* U+F0C5 "" */
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xd, 0x10, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf, 0xd1, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xf, 0xfd, 0xdf, 0xf0, 0xff,
    0xff, 0xff, 0x20, 0x0, 0xff, 0xf0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xdf, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,

    /* U+F0C7 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0xff, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xd1, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x11, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x11, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8,

    /* U+F0C9 "" */
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x12, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x12, 0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x12, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x21,

    /* U+F0E0 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0xd2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x2d,
    0xff, 0x62, 0xcf, 0xff, 0xff, 0xfc, 0x26, 0xff,
    0xff, 0xfa, 0x18, 0xff, 0xff, 0x81, 0xaf, 0xff,
    0xff, 0xff, 0xe3, 0x4d, 0xd4, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x81, 0x18, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,

    /* U+F0E7 "" */
    0x0, 0xdf, 0xff, 0xfd, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xd0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0xe, 0xff, 0xff, 0xff, 0xff, 0x20,
    0xd, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x2f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x4, 0xde, 0x40, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0x99, 0xff, 0xfd, 0x0, 0x0, 0xff, 0xff,
    0x99, 0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xd, 0xff, 0xff,
    0xd, 0x10, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf,
    0xd1, 0xff, 0xff, 0xf, 0xff, 0xff, 0xf, 0xfd,
    0xff, 0xff, 0xf, 0xff, 0xff, 0x20, 0x0, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xfd,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xfc, 0x20, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xee, 0x40, 0x0, 0x0,

    /* U+F11C "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0, 0xf0,
    0xf, 0x0, 0xff, 0xff, 0x0, 0xf0, 0xf, 0x0,
    0xf0, 0xf, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8,
    0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff, 0xf8,
    0x8, 0x80, 0x88, 0x8, 0x80, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf, 0x0,
    0xff, 0xff, 0x0, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xaf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xcf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xdf, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x17,
    0xef, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x18,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x2a,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F15B "" */
    0xdf, 0xff, 0xff, 0xf0, 0xd1, 0x0, 0xff, 0xff,
    0xff, 0xf0, 0xfd, 0x10, 0xff, 0xff, 0xff, 0xf0,
    0xff, 0xd1, 0xff, 0xff, 0xff, 0xf0, 0xff, 0xfd,
    0xff, 0xff, 0xff, 0xf2, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfd,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe,
    0xc9, 0x40, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x70, 0x0, 0x4, 0xdf,
    0xff, 0xfc, 0xa8, 0x8a, 0xcf, 0xff, 0xfd, 0x40,
    0x6f, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x5d,
    0xff, 0xf6, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xfc, 0x1a, 0x30, 0x0, 0x5a,
    0xdf, 0xfd, 0xa5, 0x0, 0x3, 0xa1, 0x0, 0x0,
    0x4d, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xfe, 0xa8, 0x8a, 0xef, 0xff,
    0x50, 0x0, 0x0, 0x1, 0xdf, 0x70, 0x0, 0x0,
    0x7, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x12, 0x0,
    0x0, 0x0, 0x0, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xe4, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F241 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F242 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F243 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0xf,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xf, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xf, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F244 "" */
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xcf, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb9, 0x29, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x10, 0x2,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xdf, 0x80, 0xa,
    0x90, 0x0, 0x0, 0x0, 0x3, 0x70, 0x0, 0xdf,
    0xff, 0x77, 0xf7, 0x55, 0x55, 0x55, 0x55, 0x8f,
    0xd3, 0xf, 0xff, 0xfd, 0xcc, 0xdf, 0xdc, 0xcc,
    0xcc, 0xcd, 0xff, 0xb0, 0x8f, 0xfe, 0x10, 0x0,
    0xaa, 0x0, 0x0, 0x0, 0x4d, 0x40, 0x0, 0x46,
    0x10, 0x0, 0x1, 0xf2, 0x2, 0x33, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xb1, 0xcf,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x22,
    0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x18, 0xdf, 0xfd, 0x92, 0x0, 0x2, 0xef,
    0xfb, 0xef, 0xff, 0x40, 0xd, 0xff, 0xfa, 0x2e,
    0xff, 0xe0, 0x4f, 0xff, 0xfa, 0x3, 0xff, 0xf5,
    0x9f, 0xfa, 0xfa, 0x35, 0x4f, 0xfa, 0xcf, 0xc0,
    0x8a, 0x3d, 0xb, 0xfd, 0xef, 0xfb, 0x3, 0x11,
    0x8f, 0xfe, 0xff, 0xff, 0xb0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xd1, 0x8, 0xff, 0xff, 0xef, 0xfd,
    0x11, 0x10, 0x9f, 0xff, 0xdf, 0xd1, 0x59, 0x3b,
    0xb, 0xfd, 0xaf, 0xd6, 0xfa, 0x37, 0x1d, 0xfb,
    0x5f, 0xff, 0xfa, 0x1, 0xdf, 0xf7, 0xd, 0xff,
    0xfa, 0x1d, 0xff, 0xf1, 0x3, 0xef, 0xfc, 0xdf,
    0xff, 0x50, 0x0, 0x18, 0xdf, 0xfe, 0xa3, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x7f, 0xff, 0xf7, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9, 0x9f,
    0xf0, 0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0,
    0xf, 0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf,
    0xf8, 0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8,
    0x8f, 0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f,
    0x88, 0xf8, 0x8f, 0xf0, 0xf, 0xf8, 0x8f, 0x88,
    0xf8, 0x8f, 0xf0, 0xf, 0xf9, 0x9f, 0x99, 0xf9,
    0x9f, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x7a, 0x1d,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfa,
    0x1d, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xfa, 0x1d, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xde, 0xdb, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe4, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x1d, 0xff, 0xff,
    0xfa, 0xef, 0xfe, 0xaf, 0xff, 0xff, 0x1, 0xdf,
    0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa, 0xff, 0xff,
    0x1d, 0xff, 0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e,
    0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x2, 0xef, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x2, 0xef, 0xff, 0xff, 0x1d, 0xff,
    0xff, 0xff, 0xe2, 0x2, 0x20, 0x2e, 0xff, 0xff,
    0x1, 0xdf, 0xff, 0xff, 0xa0, 0x2e, 0xe2, 0xa,
    0xff, 0xff, 0x0, 0x1d, 0xff, 0xff, 0xfa, 0xef,
    0xfe, 0xaf, 0xff, 0xff, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x1b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F7C2 "" */
    0x0, 0x7, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0x7, 0xf8, 0xf, 0xb,
    0x40, 0xff, 0x7f, 0xf8, 0xf, 0xb, 0x40, 0xff,
    0xff, 0xf8, 0xf, 0xb, 0x40, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xe4,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xe0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x2,
    0xef, 0x10, 0x0, 0xbf, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf1, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x11, 0xcf, 0xff, 0x77, 0x77, 0x77,
    0x77, 0xbf, 0xf1, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x17, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x7, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 66, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 79, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 18, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 36, .adv_w = 147, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 90, .adv_w = 141, .box_w = 8, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 154, .adv_w = 236, .box_w = 15, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 252, .adv_w = 199, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 324, .adv_w = 50, .box_w = 3, .box_h = 6, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 333, .adv_w = 93, .box_w = 5, .box_h = 18, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 378, .adv_w = 93, .box_w = 5, .box_h = 18, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 423, .adv_w = 122, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 455, .adv_w = 148, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 500, .adv_w = 84, .box_w = 3, .box_h = 5, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 508, .adv_w = 89, .box_w = 5, .box_h = 1, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 511, .adv_w = 84, .box_w = 3, .box_h = 3, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 516, .adv_w = 90, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 564, .adv_w = 143, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 618, .adv_w = 121, .box_w = 6, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 657, .adv_w = 143, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 705, .adv_w = 143, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 753, .adv_w = 142, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 807, .adv_w = 143, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 861, .adv_w = 143, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 920, .adv_w = 141, .box_w = 8, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 968, .adv_w = 143, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1022, .adv_w = 144, .box_w = 9, .box_h = 14, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1085, .adv_w = 84, .box_w = 3, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1099, .adv_w = 84, .box_w = 3, .box_h = 12, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1117, .adv_w = 148, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1162, .adv_w = 148, .box_w = 9, .box_h = 5, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 1185, .adv_w = 148, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 1225, .adv_w = 111, .box_w = 5, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1260, .adv_w = 233, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 1365, .adv_w = 184, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1437, .adv_w = 172, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1503, .adv_w = 177, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1569, .adv_w = 197, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1641, .adv_w = 167, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1701, .adv_w = 161, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1761, .adv_w = 190, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1833, .adv_w = 218, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1911, .adv_w = 104, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1947, .adv_w = 103, .box_w = 7, .box_h = 15, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2000, .adv_w = 187, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2072, .adv_w = 160, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2132, .adv_w = 250, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2222, .adv_w = 204, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2300, .adv_w = 196, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2372, .adv_w = 164, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2432, .adv_w = 196, .box_w = 12, .box_h = 17, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 2534, .adv_w = 183, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2606, .adv_w = 145, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2660, .adv_w = 169, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2726, .adv_w = 204, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2804, .adv_w = 183, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2876, .adv_w = 270, .box_w = 17, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2978, .adv_w = 179, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3044, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3110, .adv_w = 155, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3170, .adv_w = 88, .box_w = 4, .box_h = 16, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3202, .adv_w = 90, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3250, .adv_w = 88, .box_w = 4, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 3282, .adv_w = 148, .box_w = 7, .box_h = 6, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 3303, .adv_w = 144, .box_w = 11, .box_h = 1, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 3309, .adv_w = 111, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 3317, .adv_w = 143, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3358, .adv_w = 163, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3433, .adv_w = 138, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3469, .adv_w = 161, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3544, .adv_w = 140, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3585, .adv_w = 99, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3641, .adv_w = 145, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3700, .adv_w = 170, .box_w = 11, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3777, .adv_w = 85, .box_w = 5, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3810, .adv_w = 80, .box_w = 6, .box_h = 17, .ofs_x = -2, .ofs_y = -4},
    {.bitmap_index = 3861, .adv_w = 155, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3931, .adv_w = 86, .box_w = 5, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3966, .adv_w = 249, .box_w = 16, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4038, .adv_w = 169, .box_w = 11, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4088, .adv_w = 153, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4129, .adv_w = 163, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4194, .adv_w = 155, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4259, .adv_w = 118, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4295, .adv_w = 121, .box_w = 7, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4327, .adv_w = 94, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4363, .adv_w = 167, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4413, .adv_w = 140, .box_w = 10, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4458, .adv_w = 215, .box_w = 15, .box_h = 9, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4526, .adv_w = 144, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4567, .adv_w = 141, .box_w = 10, .box_h = 13, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 4632, .adv_w = 127, .box_w = 8, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4668, .adv_w = 96, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4716, .adv_w = 81, .box_w = 1, .box_h = 19, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 4726, .adv_w = 96, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4774, .adv_w = 148, .box_w = 9, .box_h = 3, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 4788, .adv_w = 256, .box_w = 17, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4916, .adv_w = 256, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5029, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5157, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5285, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5413, .adv_w = 256, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5533, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5661, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5789, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5917, .adv_w = 256, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6053, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6149, .adv_w = 256, .box_w = 16, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6261, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6357, .adv_w = 176, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6423, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6551, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6679, .adv_w = 288, .box_w = 18, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6805, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6933, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7041, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7169, .adv_w = 128, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7225, .adv_w = 192, .box_w = 12, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7309, .adv_w = 288, .box_w = 18, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7453, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7549, .adv_w = 176, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7637, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 7717, .adv_w = 224, .box_w = 14, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7843, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7948, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8046, .adv_w = 224, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 8126, .adv_w = 224, .box_w = 16, .box_h = 14, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 8238, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8308, .adv_w = 160, .box_w = 10, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8378, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 8476, .adv_w = 224, .box_w = 14, .box_h = 4, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 8504, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8612, .adv_w = 320, .box_w = 20, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8772, .adv_w = 288, .box_w = 20, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 8932, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9060, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 9130, .adv_w = 224, .box_w = 14, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 9200, .adv_w = 320, .box_w = 20, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9340, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9436, .adv_w = 256, .box_w = 16, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9564, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9709, .adv_w = 224, .box_w = 15, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9814, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9926, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10024, .adv_w = 224, .box_w = 14, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 10122, .adv_w = 256, .box_w = 16, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10218, .adv_w = 160, .box_w = 12, .box_h = 16, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 10314, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10426, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10538, .adv_w = 288, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10646, .adv_w = 256, .box_w = 18, .box_h = 18, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10808, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10904, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11054, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11154, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11254, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11354, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11454, .adv_w = 320, .box_w = 20, .box_h = 10, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11554, .adv_w = 320, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 11701, .adv_w = 224, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11797, .adv_w = 224, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11909, .adv_w = 256, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 12054, .adv_w = 320, .box_w = 20, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12174, .adv_w = 192, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12270, .adv_w = 258, .box_w = 17, .box_h = 11, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x3d1, 0xb1b, 0x11d4, 0x1bf8, 0x1d0b, 0x2b0d, 0x2e6b,
    0x4543, 0x9fc5, 0x9fcc, 0x9fcf, 0x9fd0, 0x9fd1, 0x9fd5, 0x9fd7,
    0x9fd9, 0x9fdd, 0x9fe0, 0x9fe5, 0x9fea, 0x9feb, 0x9fec, 0xa002,
    0xa007, 0xa00c, 0xa00f, 0xa010, 0xa011, 0xa015, 0xa016, 0xa017,
    0xa018, 0xa02b, 0xa02c, 0xa032, 0xa034, 0xa035, 0xa038, 0xa03b,
    0xa03c, 0xa03d, 0xa03f, 0xa057, 0xa059, 0xa088, 0xa089, 0xa08b,
    0xa08d, 0xa0a4, 0xa0ab, 0xa0ae, 0xa0b7, 0xa0e0, 0xa0e8, 0xa11f,
    0xa1af, 0xa204, 0xa205, 0xa206, 0xa207, 0xa208, 0xa24b, 0xa257,
    0xa2b1, 0xa2c8, 0xa51e, 0xa786, 0xa866
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20540, .range_length = 43111, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 69, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 2, 0, 0, 3,
    1, 4, 5, 6, 0, 7, 8, 7,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 10, 10, 0, 0, 0,
    11, 12, 13, 14, 15, 16, 17, 18,
    19, 20, 20, 21, 22, 23, 20, 24,
    25, 26, 25, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 4, 36, 5, 0,
    37, 0, 38, 39, 40, 41, 42, 43,
    44, 45, 46, 47, 48, 41, 45, 45,
    39, 39, 49, 50, 51, 52, 53, 54,
    54, 55, 54, 56, 4, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 3, 0, 0, 4,
    2, 5, 6, 7, 0, 8, 9, 10,
    11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 12, 13, 0, 0, 0,
    14, 15, 16, 17, 18, 17, 17, 17,
    18, 17, 17, 19, 17, 17, 20, 20,
    18, 17, 18, 17, 21, 22, 23, 24,
    25, 26, 27, 28, 5, 29, 6, 0,
    30, 0, 31, 32, 33, 33, 33, 34,
    35, 32, 36, 37, 32, 32, 38, 38,
    33, 39, 33, 38, 40, 41, 42, 43,
    43, 44, 45, 46, 5, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 0, -29,
    0, -29, -22, 0, 0, 0, 0, -34,
    0, -7, -3, 0, -3, 8, -1, 0,
    0, 0, 0, 0, 0, 0, -8, 0,
    -11, -1, -14, 0, 0, -2, -5, -8,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -8, 0, 0, 0, 0, -10, 0, 4,
    0, 0, 0, 0, 0, -5, -5, 0,
    -1, 0, 0, 0, 0, 0, 0, -1,
    -2, 0, 0, 0, 0, 0, 3, 0,
    1, 0, 1, 0, 0, 0, 0, 0,
    0, -5, 0, -2, -6, -2, 0, 0,
    0, 0, 0, 0, 0, 0, -2, 0,
    0, -15, -8, -17, -16, 0, -16, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, -5, -14, 3,
    0, 0, -11, 0, 0, -11, -11, 0,
    0, -8, 0, -5, 9, 0, -3, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -8, -1, 0, 0, 27, -4,
    0, -5, -3, -6, -5, -3, 4, -3,
    0, 0, 0, 0, 0, -14, 0, -11,
    0, -11, 0, 0, 0, 0, 0, -8,
    0, 0, 0, 0, 0, -5, -5, -14,
    -11, -5, -11, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -28, 0, -28,
    0, 0, 0, 0, 0, -31, 0, -3,
    -5, 0, 0, 6, 0, 0, 0, -2,
    0, 0, 0, 0, -10, 0, -7, -2,
    -12, 0, 0, -2, -3, -6, 0, 0,
    0, 0, 0, -4, 0, -22, 0, 0,
    -14, -12, -36, 0, -11, 0, 0, 0,
    0, 0, 0, 0, 0, -15, 0, 0,
    0, -22, -17, -37, -33, 0, -33, 0,
    -36, 0, -5, 0, -8, 0, -5, 0,
    2, 0, -8, -4, -11, -12, -25, 0,
    -11, -5, 0, 0, 0, 0, 0, -11,
    0, -14, 0, -14, -8, 0, 0, 0,
    0, -8, 0, 3, -5, -3, 0, -11,
    -3, -22, -19, -11, -19, -3, -11, 0,
    0, -1, 3, -2, 1, 0, 0, 0,
    0, 0, 0, 0, -2, -5, -2, 0,
    0, 0, -5, 0, 0, 0, 0, -38,
    -17, -38, -21, 0, 0, 0, 0, -19,
    0, 0, 0, 0, 0, 1, 0, 3,
    3, 0, 0, 0, 0, 0, -9, 0,
    -8, 0, -9, 0, 0, -5, -5, -5,
    0, -4, 0, -3, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, -3, -8, -8, -2,
    -14, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, -31, 0, -31, -19, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -4, 0, 0, 0, 0,
    0, -2, 0, 1, 0, 0, 0, 0,
    0, -1, -1, -1, -1, 0, 0, -22,
    0, -3, 0, -1, -3, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 1, 0,
    0, -31, 0, 0, -5, -8, -30, 0,
    -9, 0, 0, 0, 0, 0, 0, 3,
    -3, -10, -3, -2, 0, -16, -18, -34,
    -28, 0, -25, 0, -17, 0, -1, 0,
    -5, 0, -1, 0, -2, 0, -8, 0,
    -5, -5, -15, 0, -12, 0, 0, -5,
    0, 0, 0, -4, -3, -7, 0, -7,
    -5, 0, 0, 0, 0, -8, -7, -2,
    -3, -4, 0, -5, -8, -14, -10, -8,
    -14, -2, -4, -7, 0, -3, 0, -4,
    0, 0, 0, 0, -3, 0, 0, -4,
    -5, -3, -2, 0, 0, 5, 0, 0,
    0, 1, 1, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, -3, -3, -3, 0, -5, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -3, 0, 0,
    0, 0, 0, -1, 0, 0, 0, -5,
    0, -12, 1, -12, -8, 0, 0, 0,
    4, -15, -7, 3, -3, -5, 0, -5,
    -5, -12, -13, -11, -17, -2, -4, -19,
    0, -3, 0, 0, -3, 0, 0, 0,
    -3, -3, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -4,
    -4, 0, -4, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, -5, 0,
    0, -3, -5, 0, -5, 0, 0, 3,
    0, -2, -3, 0, 3, -26, -5, -26,
    -17, -1, -1, 0, -6, -20, -3, -2,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 4, -33, -11, -1, -12, -8,
    -17, -2, 0, -5, -11, -12, 0, -5,
    -5, -8, -5, -8, 0, 0, 0, 0,
    3, -1, 0, -2, 6, -2, -4, 0,
    0, 0, 3, -3, -2, 3, 0, 0,
    0, -2, -4, -10, -10, -5, -12, -2,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -5, 0, -6, -5, -5, 0, 0, 0,
    -9, -5, -3, -6, -10, 0, -8, 0,
    0, 0, 0, 0, 0, 0, 0, -15,
    -3, -15, -8, -6, -6, 0, -4, -10,
    0, -3, 0, 0, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -20, -7, 0,
    -6, -10, -8, -2, -2, -7, -10, -8,
    -5, -8, -8, -5, -8, -5, 0, 0,
    0, 0, 0, 0, 0, 0, -8, 0,
    0, 0, 0, 0, 0, 3, 0, -8,
    -4, 0, 0, 0, -3, -2, -2, 0,
    -3, 2, 0, 0, -2, 0, -5, 0,
    -3, 0, 0, 0, -8, 0, -5, -5,
    -15, 0, -13, 0, 0, -36, 0, 0,
    0, -7, -28, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -5, -4, -5, -2,
    -4, -20, -12, -30, -26, -4, -28, -4,
    -15, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -5, 0, -2, -6, -10, 0,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, -8, -1, -8, -8, -4, -4, 0,
    0, -10, -2, -4, 0, 0, -2, 0,
    -3, 0, 0, -3, 0, -1, 0, -15,
    -4, 0, -3, -5, -5, 0, 0, -3,
    -6, -3, -3, -5, -5, -5, -5, -5,
    0, -7, 0, 0, 0, -5, -3, -11,
    3, -11, -5, 0, 0, 0, 5, -10,
    -6, 3, -2, -4, 0, -2, -5, -10,
    -7, -8, -11, 0, -2, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    0, -2, 0, -2, 6, -28, -1, -28,
    -14, 2, 2, 0, 0, -17, -3, 0,
    -3, 0, 2, 3, -5, -3, -3, -5,
    -6, 3, 3, -40, -1, -1, -5, 0,
    -5, 0, 0, -2, 0, -1, 4, 0,
    2, 0, 4, 0, 0, -11, 0, 0,
    0, 0, -4, 0, -4, 0, 0, 0,
    0, 0, 0, 3, 0, -5, -3, 0,
    0, -8, -9, -15, -13, 2, -13, 2,
    -5, 5, 0, 0, -3, 0, 0, 0,
    0, 0, -4, 0, -3, -4, -5, 0,
    -5, 0, 0, 0, 0, 0, 0, -3,
    0, -5, 0, -5, -4, 0, 0, 0,
    1, -5, -2, 0, -2, -2, -2, -3,
    -7, -7, -7, -3, -8, 0, 0, -11,
    0, -2, 0, -4, -2, 0, 0, 0,
    -3, 0, -3, -3, -5, -2, -5, 0,
    0, 8, 0, 0, -5, 0, 6, -22,
    -11, -22, -13, -1, -1, 0, -5, -18,
    0, -2, 0, 0, 0, 3, -3, 0,
    0, -1, 0, 2, 7, -23, -11, 0,
    -19, -3, -11, 0, 0, -8, -18, -14,
    0, -8, -11, -10, -11, -17, 0, 0,
    0, -4, -5, -3, 0, -22, -4, -22,
    -15, -4, -4, 0, 0, -20, -5, -7,
    -4, -3, -5, -3, -5, -5, -2, -3,
    0, -2, 0, -23, -11, -2, -8, -5,
    -11, -5, -2, -7, -12, -11, -5, -8,
    -6, -9, -8, -10, 0, 0, 0, -11,
    -14, 0, 0, -42, -20, -42, -24, -11,
    -11, 0, -13, -31, 0, -12, 0, 0,
    -5, 0, -3, 0, 0, 0, 0, -3,
    3, -44, -22, -1, -20, -14, -23, -8,
    -5, -16, -20, -24, -8, -17, -11, -19,
    -17, -17, 0, 0, 0, -8, -11, 0,
    0, -34, -18, -34, -25, -11, -11, 0,
    -13, -29, 0, -10, 0, -2, -5, 0,
    -3, 0, 0, 0, 0, -3, 3, -36,
    -20, -1, -21, -11, -23, -5, -5, -14,
    -19, -20, -7, -15, -12, -15, -12, -17,
    0, 0, 0, 0, -5, 0, -2, 0,
    -12, 0, 0, -2, -2, 0, 0, 0,
    0, -8, 0, 0, 0, -3, -3, 0,
    0, 0, 0, 2, 0, 0, -1, 0,
    -8, 0, 0, 0, -4, 0, -9, -3,
    -8, -11, -18, 0, -13, 0, 0, 0,
    0, -10, -14, 0, 0, -29, -22, -29,
    -25, -17, -17, 0, -13, -25, 0, -14,
    0, 0, -11, 0, 0, 0, 0, 0,
    0, 0, 4, -30, -22, -1, -26, -17,
    -27, -11, -7, -18, -25, -23, -17, -23,
    -19, -17, -19, -24, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, -2, 0, 0,
    0, -3, -5, -3, -3, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -4, -7, 0,
    -7, 0, 0, -22, 0, 0, 0, 0,
    0, 4, -5, 4, 0, 0, 0, 0,
    0, 0, 0, -8, 19, 0, 0, -17,
    -15, -26, -23, 0, -22, 0, -21, 0,
    0, 0, 0, 0, 1, 0, 24, 0,
    0, 0, -3, -1, -5, 0, 8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -15, 9, 0, -5, -23, -28, -42,
    -39, 0, -30, 0, 0, 0, -4, 0,
    -14, 0, 0, 0, 0, 0, -6, -5,
    -14, -13, -20, 0, -16, 0, 0, -17,
    0, 0, 0, 0, -12, 0, -3, 0,
    0, 0, 0, -1, 0, 2, -2, -4,
    -5, 0, 0, -15, -14, -27, -25, 0,
    -22, 0, -12, 0, 0, 0, 0, 0,
    -2, 0, 0, -1, -5, 0, 0, 0,
    -5, 0, -3, 0, 0, -11, 0, 0,
    0, -4, -7, -7, 3, -7, -5, -1,
    -1, -1, 0, -11, -6, 0, -6, -2,
    -2, -17, -10, -21, -18, -5, -21, 0,
    -5, -14, 0, -2, 0, 0, 0, 0,
    0, 0, -2, 0, -2, -2, 0, -5,
    -3, 0, 0, -1, 0, 0, 0, 0,
    -1, -1, 0, -1, 0, 0, 0, 0,
    0, -3, -4, 0, -5, 0, 0, -11,
    -8, -19, -17, -5, -17, 0, -1, -7,
    0, -3, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -3, 0, -1, -5, 0,
    -1, 0, 0, 0, 0, -1, 0, 0,
    0, 0, -1, 0, 0, -6, -7, -11,
    -10, 0, -12, 0, -1, 0, -2, 0,
    0, 0, -2, 0, 0, 0, -5, 0,
    0, -3, -3, 0, -3, 0, 0, -5,
    0, 0, 0, -4, -5, -7, 0, -7,
    -2, 0, 0, -1, 0, -5, -3, 0,
    -5, 0, 0, -13, -8, -18, -16, -5,
    -20, 0, -5, -11, -2, -3, 0, -2,
    0, 0, 0, 0, -2, 0, 0, 0,
    -2, 0, -2, 0, 10, 22, 0, 0,
    0, 25, 15, -10, -3, -10, -5, 0,
    0, 10, 0, -2, 5, 0, 7, 11,
    6, 15, 11, 15, 15, 13, 15, 11,
    27, -6, 0, -3, -4, 0, -1, 6,
    6, 0, 0, 0, 0, -3, 3, 0,
    3, 0, 0, 0, 0, -5, 0, 0,
    0, 0, -1, 0, 4, 0, 0, -1,
    0, 0, -2, 0, 13, -1, 0, -7,
    -9, -11, -11, 0, -18, 0, -4, 0,
    -5, -3, -2, 0, -2, 0, 5, 0,
    0, 0, 0, 0, 1, 0, 2, 0,
    0, -15, 0, 0, 0, -4, -18, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    -1, -1, -5, 0, 0, -16, -9, -24,
    -22, 0, -19, 0, -14, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, 0,
    -1, -1, -7, 0, -7, 0, -1, -2,
    0, -1, 0, 0, -1, 0, 0, 0,
    0, 0, 0, -2, 0, 0, -1, 0,
    0, 0, 0, -5, -5, -5, -5, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -4, 0, -4, 0, 0, -1, 0, -1,
    0, 0, -1, -4, 0, -4, -3, -4,
    -4, -1, 0, -4, -2, -1, 0, 0,
    0, 0, -7, -5, -5, -3, -5, 0,
    0, -9, 0, -1, 0, 0, -3, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    -1, 0, -5, 0, 0, -1, -1, -1,
    0, 4, -1, -1, -5, 0, 0, -11,
    -11, -18, -15, 1, -19, 2, -1, 0,
    0, 0, -5, 0, 0, 0, 0, 0,
    -3, -2, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -1, 0, 3, -6, 8,
    0, 3, 3, 0, 8, -1, 0, -4,
    -6, 0, 8, -4, 0, -14, -12, -22,
    -18, -1, -22, 0, -4, -1, -2, -2,
    0, 0, 0, 0, 10, 0, 0, 0,
    0, 0, 0, 0, 5, 0, 0, 1,
    0, -3, 0, -6, 4, -24, -8, -24,
    -13, -2, -2, -1, -3, -15, -6, 0,
    -8, -4, 0, -1, -8, -13, -11, -15,
    -13, 0, 0, -18, -3, -3, -4, 0,
    -4, 0, 0, 0, 0, -2, 5, 0,
    3, 0, 3, 0, 0, -5, 0, 0,
    0, -4, -4, -6, 0, -6, 0, 0,
    0, 0, 0, -3, -5, 0, -7, -3,
    0, -9, -6, -19, -17, 0, -24, 0,
    -6, -10, 0, -3, 0, -1, 0, 0,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 1, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 4, 0, 0, -3, 0, 0, 0,
    -3, -11, -8, 0, -12, 2, 0, 3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 2, 0, 0, 0,
    0, -10, 0, 0, 0, -6, -10, 0,
    0, 0, 0, -3, -3, 0, 0, 0,
    0, -3, -7, 0, 0, -10, -10, -21,
    -19, 0, -20, 0, -5, 0, 0, 0,
    -2, 0, -3, 0, -2, 0, -3, 0,
    0, 0, -5, 0, -5, 0, 0, -1,
    0, -4, 0, -9, -1, -26, -4, -26,
    -11, -1, -1, -1, -2, -18, -7, 0,
    -10, -5, 0, -4, -6, -14, -12, -14,
    -15, -2, 0, -22, -4, -6, -5, 0,
    -5, 0, 0, 0, 0, -2, 2, 0,
    3, 0, 3, 0, 0, 0, 0, 0,
    0, -4, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 2, 0, -2, -5, 0,
    0, -8, -8, -19, -14, 0, -17, 0,
    0, 0, -3, 0, -5, 0, -2, 0,
    0, 0, -5, 0, 0, 0, 0, 1,
    0, 0, 0, 0, 0, 0, 0, -3,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -7, -17, -14, 0, -16, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 56,
    .right_class_cnt     = 46,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_SourceHanSerifSC_Regular_16 = {
#else
lv_font_t lv_font_SourceHanSerifSC_Regular_16 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 16,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_SOURCEHANSERIFSC_REGULAR_16*/

