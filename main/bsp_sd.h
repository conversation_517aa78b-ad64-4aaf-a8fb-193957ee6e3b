
#ifndef __BSP_SD_H__
#define __BSP_SD_H__

// #include "bsp_power_init.h"
#include "file_iterator.h"

#include "esp_spiffs.h"
#include "esp_vfs_fat.h"
#include "sdmmc_cmd.h"
#include "driver/sdmmc_host.h"

#define MOUNT_POINT              "/sdcard"
#define EXAMPLE_MAX_CHAR_SIZE    64
#define BSP_SD_CLK          (47)
#define BSP_SD_CMD          (48)
#define BSP_SD_D0           (21)


void sd_card_task(void *pvParameters);

/* 
esp_err_t s_example_write_file(const char *path, char *data);
esp_err_t s_example_read_file(const char *path);
 */

#endif // __BSP_SD_H__
