#include <stdio.h>
#include "esp_err.h"
#include "esp_log.h"            //日志
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "bsp_sd.h"
#include "bsp_lcd.h"          // LCD驱动
#include "lcd_test.h"         // LCD测试
#include "esp_task_wdt.h"     // 看门狗
#include <dirent.h>           // 目录操作
#include <sys/stat.h>         // 文件状态
#include <string.h>           // 字符串操作
#include "cJSON.h"            // JSON解析

static const char *TAG_APP = "MAIN";
static const char *TAG_FLOWER = "FLOWER";

/**
 * @brief 花朵信息结构体
 */
typedef struct {
    int id;
    char name[64];
    int level;
    int growth;
    int water;
    char image[64];
    char thumbnail[64];
    char description[128];
} flower_info_t;

/**
 * @brief 从JSON文件读取花朵信息
 * @param file_path JSON文件完整路径
 * @param flower 输出的花朵信息结构体
 * @return esp_err_t
 */
esp_err_t read_flower_from_json(const char* file_path, flower_info_t* flower)
{
    FILE* file = fopen(file_path, "r");
    if (!file) {
        ESP_LOGE(TAG_FLOWER, "无法打开文件: %s", file_path);
        return ESP_FAIL;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    // 分配缓冲区
    char* json_string = malloc(file_size + 1);
    if (!json_string) {
        fclose(file);
        ESP_LOGE(TAG_FLOWER, "内存分配失败");
        return ESP_ERR_NO_MEM;
    }

    // 读取文件内容
    size_t read_size = fread(json_string, 1, file_size, file);
    json_string[read_size] = '\0';
    fclose(file);

    // 解析JSON
    cJSON* json = cJSON_Parse(json_string);
    free(json_string);
    
    if (!json) {
        ESP_LOGE(TAG_FLOWER, "JSON解析失败: %s", file_path);
        return ESP_FAIL;
    }

    // 提取字段
    cJSON* id = cJSON_GetObjectItem(json, "id");
    cJSON* name = cJSON_GetObjectItem(json, "name");
    cJSON* level = cJSON_GetObjectItem(json, "level");
    cJSON* growth = cJSON_GetObjectItem(json, "growth");
    cJSON* water = cJSON_GetObjectItem(json, "water");
    cJSON* image = cJSON_GetObjectItem(json, "image");
    cJSON* thumbnail = cJSON_GetObjectItem(json, "thumbnail");
    cJSON* description = cJSON_GetObjectItem(json, "description");

    // 填充结构体
    flower->id = cJSON_IsNumber(id) ? id->valueint : 0;
    strncpy(flower->name, cJSON_IsString(name) ? name->valuestring : "未知", sizeof(flower->name) - 1);
    flower->level = cJSON_IsNumber(level) ? level->valueint : 1;
    flower->growth = cJSON_IsNumber(growth) ? growth->valueint : 0;
    flower->water = cJSON_IsNumber(water) ? water->valueint : 0;
    strncpy(flower->image, cJSON_IsString(image) ? image->valuestring : "", sizeof(flower->image) - 1);
    strncpy(flower->thumbnail, cJSON_IsString(thumbnail) ? thumbnail->valuestring : "", sizeof(flower->thumbnail) - 1);
    strncpy(flower->description, cJSON_IsString(description) ? description->valuestring : "", sizeof(flower->description) - 1);

    cJSON_Delete(json);
    
    ESP_LOGI(TAG_FLOWER, "成功读取花朵: %s (ID:%d, 等级:%d)", flower->name, flower->id, flower->level);
    return ESP_OK;
}

/**
 * @brief 读取RGB565格式图片文件到内存
 * @param image_name 图片文件名（推荐.bin或.rgb565格式）
 * @param buffer 输出缓冲区指针
 * @param size 输出文件大小
 * @return esp_err_t
 */
esp_err_t read_flower_image(const char* image_name, uint8_t** buffer, size_t* size)
{
    char image_path[512];
    snprintf(image_path, sizeof(image_path), "/sdcard/flower/image/%s", image_name);

    FILE* file = fopen(image_path, "rb");
    if (!file) {
        ESP_LOGE(TAG_FLOWER, "无法打开图片文件: %s", image_path);
        return ESP_FAIL;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    *size = ftell(file);
    fseek(file, 0, SEEK_SET);

    // 分配内存
    *buffer = malloc(*size);
    if (!*buffer) {
        fclose(file);
        ESP_LOGE(TAG_FLOWER, "图片内存分配失败");
        return ESP_ERR_NO_MEM;
    }

    // 读取文件
    size_t read_size = fread(*buffer, 1, *size, file);
    fclose(file);

    if (read_size != *size) {
        free(*buffer);
        *buffer = NULL;
        ESP_LOGE(TAG_FLOWER, "图片读取不完整");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG_FLOWER, "成功读取图片: %s (%zu bytes)", image_name, *size);
    return ESP_OK;
}

/**
 * @brief 显示RGB565格式的花朵图片到LCD
 * @param image_name 图片文件名
 * @param x 显示位置X坐标
 * @param y 显示位置Y坐标
 * @param width 图片宽度
 * @param height 图片高度
 * @return esp_err_t
 */
esp_err_t display_flower_image_to_lcd(const char* image_name, uint16_t x, uint16_t y, uint16_t width, uint16_t height)
{
    uint8_t* image_buffer = NULL;
    size_t image_size = 0;

    // 读取图片数据
    esp_err_t ret = read_flower_image(image_name, &image_buffer, &image_size);
    if (ret != ESP_OK) {
        return ret;
    }

    // 验证图片大小是否匹配
    size_t expected_size = width * height * 2; // RGB565每像素2字节
    if (image_size != expected_size) {
        ESP_LOGE(TAG_FLOWER, "图片大小不匹配: 期望%zu字节, 实际%zu字节", expected_size, image_size);
        free(image_buffer);
        return ESP_ERR_INVALID_SIZE;
    }

    // 显示到LCD（需要包含bsp_lcd.h）
    // 注意：这里假设image_buffer是RGB565格式的数据
    // ret = bsp_lcd_draw_bitmap(x, y, x + width - 1, y + height - 1, (uint16_t*)image_buffer);

    ESP_LOGI(TAG_FLOWER, "图片显示完成: %s (%dx%d)", image_name, width, height);

    free(image_buffer);
    return ESP_OK;
}

/**
 * @brief 扫描并读取所有花朵信息
 * @param flowers 花朵信息数组
 * @param max_count 最大花朵数量
 * @param actual_count 实际读取的花朵数量
 * @return esp_err_t
 */
esp_err_t scan_all_flowers(flower_info_t* flowers, int max_count, int* actual_count)
{
    const char* data_dir = "/sdcard/flower/data";
    DIR* dir = opendir(data_dir);
    if (!dir) {
        ESP_LOGE(TAG_FLOWER, "无法打开花朵数据目录: %s", data_dir);
        return ESP_FAIL;
    }

    *actual_count = 0;
    struct dirent* entry;
    
    while ((entry = readdir(dir)) != NULL && *actual_count < max_count) {
        // 只处理.json文件
        if (strstr(entry->d_name, ".json") == NULL) {
            continue;
        }

        char file_path[512];
        snprintf(file_path, sizeof(file_path), "%s/%s", data_dir, entry->d_name);

        if (read_flower_from_json(file_path, &flowers[*actual_count]) == ESP_OK) {
            (*actual_count)++;
        }
    }

    closedir(dir);
    ESP_LOGI(TAG_FLOWER, "扫描完成，共找到 %d 朵花", *actual_count);
    return ESP_OK;
}

/**
 * @brief 花朵数据管理任务
 * @param pvParameters 任务参数
 */
void flower_data_task(void *pvParameters)
{
    ESP_LOGI(TAG_FLOWER, "花朵数据管理任务启动");
    
    // 等待SD卡挂载完成
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 定义花朵数组
    const int MAX_FLOWERS = 10;
    flower_info_t flowers[MAX_FLOWERS];
    int flower_count = 0;
    
    // 扫描所有花朵
    esp_err_t ret = scan_all_flowers(flowers, MAX_FLOWERS, &flower_count);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG_FLOWER, "花朵扫描失败");
        vTaskDelete(NULL);
        return;
    }
    
    // 显示花朵信息
    for (int i = 0; i < flower_count; i++) {
        ESP_LOGI(TAG_FLOWER, "花朵 %d: %s (等级:%d, 成长:%d%%, 水分:%d%%)", 
                 flowers[i].id, flowers[i].name, flowers[i].level, 
                 flowers[i].growth, flowers[i].water);
        
        // 读取缩略图示例
        if (strlen(flowers[i].thumbnail) > 0) {
            uint8_t* image_buffer = NULL;
            size_t image_size = 0;
            
            if (read_flower_image(flowers[i].thumbnail, &image_buffer, &image_size) == ESP_OK) {
                ESP_LOGI(TAG_FLOWER, "缩略图读取成功: %zu bytes", image_size);
                // 这里可以将图片数据传递给GUI显示
                // 例如: update_gui_flower_image(i, image_buffer, image_size);
                free(image_buffer);
            }
        }
    }
    
    // 任务完成
    ESP_LOGI(TAG_FLOWER, "花朵数据处理完成");
    vTaskDelete(NULL);
}

void app_main(void)
{
    xTaskCreate(sd_card_task, "sd_card_task", 4096, NULL, 5, NULL);  // SD卡初始化任务
    vTaskDelay(pdMS_TO_TICKS(1000));                                 // 等待SD卡挂载完成
    ESP_LOGI(TAG_APP, "SD卡挂载完成");
    
    // 创建花朵数据管理任务
    xTaskCreate(flower_data_task, "flower_data_task", 8192, NULL, 4, NULL);
    
    /*
     * GUI界面启动 - 一站式初始化
     * 包括：LCD驱动、LVGL库、显示驱动、输入设备、UI界面、按键控制
     */
    printf("============ 启动GUI界面系统 =============\n");
    esp_err_t ret = start_gui_guider_ui();
    if (ret != ESP_OK) {
        printf("GUI界面启动失败: %s\n", esp_err_to_name(ret));
        printf("系统将继续运行，但无GUI界面\n");
    } else {
        printf("GUI界面启动完成，系统已准备就绪！\n");
    }

    while(1){
        // 看门狗喂狗 - 防止看门狗复位
        esp_task_wdt_reset();//必须要有
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}