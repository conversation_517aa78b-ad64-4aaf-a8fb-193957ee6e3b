#include <stdio.h>
#include "esp_err.h"
#include "esp_log.h"            // 日志系统
#include "freertos/FreeRTOS.h"  // FreeRTOS实时操作系统
#include "freertos/task.h"      // 任务管理
#include "bsp_sd.h"             // SD卡驱动
#include "bsp_lcd.h"            // LCD驱动
#include "lcd_test.h"           // LCD测试和GUI启动
#include "esp_task_wdt.h"       // 看门狗
#include <dirent.h>             // 目录操作
#include <sys/stat.h>           // 文件状态
#include <string.h>             // 字符串操作
#include "cJSON.h"              // JSON解析库
#include "ui/generated/gui_guider.h"  // GUI界面结构定义

// 日志标签定义
static const char *TAG_APP = "MAIN";
static const char *TAG_FLOWER = "FLOWER";

// 外部GUI实例声明 (定义在lcd_test.c中)
extern lv_ui guider_ui;

/**
 * @brief 花朵信息结构体
 * @details 存储从JSON文件读取的花朵完整信息
 */
typedef struct {
    int id;                     // 花朵唯一标识符
    char name[64];              // 花朵名称 (如"蒲公英")
    int level;                  // 花朵等级 (1-10)
    int growth;                 // 成长值百分比 (0-100)
    int water;                  // 水分值百分比 (0-100)
    char image[64];             // 图片文件名 (如"flower_001.bin")
    char thumbnail[64];         // 缩略图文件名 (当前未使用)
    char description[128];      // 花朵描述信息
} flower_info_t;

/**
 * @brief 从JSON文件读取花朵信息
 * @details 解析SD卡中的JSON文件，提取花朵的所有属性信息
 * @param file_path JSON文件完整路径 (如"/sdcard/flower/data/flower_001.json")
 * @param flower 输出的花朵信息结构体指针
 * @return esp_err_t
 *         - ESP_OK: 读取成功
 *         - ESP_FAIL: 文件打开失败或JSON解析失败
 *         - ESP_ERR_NO_MEM: 内存分配失败
 */
esp_err_t read_flower_from_json(const char* file_path, flower_info_t* flower)
{
    FILE* file = fopen(file_path, "r");
    if (!file) {
        ESP_LOGE(TAG_FLOWER, "无法打开文件: %s", file_path);
        return ESP_FAIL;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    long file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    // 分配缓冲区
    char* json_string = malloc(file_size + 1);
    if (!json_string) {
        fclose(file);
        ESP_LOGE(TAG_FLOWER, "内存分配失败");
        return ESP_ERR_NO_MEM;
    }

    // 读取文件内容
    size_t read_size = fread(json_string, 1, file_size, file);
    json_string[read_size] = '\0';
    fclose(file);

    // 解析JSON
    cJSON* json = cJSON_Parse(json_string);
    free(json_string);
    
    if (!json) {
        ESP_LOGE(TAG_FLOWER, "JSON解析失败: %s", file_path);
        return ESP_FAIL;
    }

    // 提取字段
    cJSON* id = cJSON_GetObjectItem(json, "id");
    cJSON* name = cJSON_GetObjectItem(json, "name");
    cJSON* level = cJSON_GetObjectItem(json, "level");
    cJSON* growth = cJSON_GetObjectItem(json, "growth");
    cJSON* water = cJSON_GetObjectItem(json, "water");
    cJSON* image = cJSON_GetObjectItem(json, "image");
    cJSON* thumbnail = cJSON_GetObjectItem(json, "thumbnail");
    cJSON* description = cJSON_GetObjectItem(json, "description");

    // 填充结构体
    flower->id = cJSON_IsNumber(id) ? id->valueint : 0;
    strncpy(flower->name, cJSON_IsString(name) ? name->valuestring : "未知", sizeof(flower->name) - 1);
    flower->level = cJSON_IsNumber(level) ? level->valueint : 1;
    flower->growth = cJSON_IsNumber(growth) ? growth->valueint : 0;
    flower->water = cJSON_IsNumber(water) ? water->valueint : 0;
    strncpy(flower->image, cJSON_IsString(image) ? image->valuestring : "", sizeof(flower->image) - 1);
    strncpy(flower->thumbnail, cJSON_IsString(thumbnail) ? thumbnail->valuestring : "", sizeof(flower->thumbnail) - 1);
    strncpy(flower->description, cJSON_IsString(description) ? description->valuestring : "", sizeof(flower->description) - 1);

    cJSON_Delete(json);
    
    ESP_LOGI(TAG_FLOWER, "成功读取花朵: %s (ID:%d, 等级:%d)", flower->name, flower->id, flower->level);
    return ESP_OK;
}

/**
 * @brief 读取RGB565格式图片文件到内存
 * @param image_name 图片文件名（推荐.bin或.rgb565格式）
 * @param buffer 输出缓冲区指针
 * @param size 输出文件大小
 * @return esp_err_t
 */
esp_err_t read_flower_image(const char* image_name, uint8_t** buffer, size_t* size)
{
    char image_path[512];
    snprintf(image_path, sizeof(image_path), "/sdcard/flower/image/%s", image_name);

    FILE* file = fopen(image_path, "rb");
    if (!file) {
        ESP_LOGE(TAG_FLOWER, "无法打开图片文件: %s", image_path);
        return ESP_FAIL;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    *size = ftell(file);
    fseek(file, 0, SEEK_SET);

    // 分配内存
    *buffer = malloc(*size);
    if (!*buffer) {
        fclose(file);
        ESP_LOGE(TAG_FLOWER, "图片内存分配失败");
        return ESP_ERR_NO_MEM;
    }

    // 读取文件
    size_t read_size = fread(*buffer, 1, *size, file);
    fclose(file);

    if (read_size != *size) {
        free(*buffer);
        *buffer = NULL;
        ESP_LOGE(TAG_FLOWER, "图片读取不完整");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG_FLOWER, "成功读取图片: %s (%zu bytes)", image_name, *size);
    return ESP_OK;
}

/**
 * @brief 显示RGB565格式的花朵图片到LCD
 * @param image_name 图片文件名
 * @param x 显示位置X坐标
 * @param y 显示位置Y坐标
 * @param width 图片宽度
 * @param height 图片高度
 * @return esp_err_t
 */
esp_err_t display_flower_image_to_lcd(const char* image_name, uint16_t x, uint16_t y, uint16_t width, uint16_t height)
{
    uint8_t* image_buffer = NULL;
    size_t image_size = 0;

    // 读取图片数据
    esp_err_t ret = read_flower_image(image_name, &image_buffer, &image_size);
    if (ret != ESP_OK) {
        return ret;
    }

    // 验证图片大小是否匹配
    size_t expected_size = width * height * 2; // RGB565每像素2字节
    if (image_size != expected_size) {
        ESP_LOGE(TAG_FLOWER, "图片大小不匹配: 期望%zu字节, 实际%zu字节", expected_size, image_size);
        free(image_buffer);
        return ESP_ERR_INVALID_SIZE;
    }

    // 🚨 问题所在：这里只是读取了图片数据，但没有实际显示到LCD！
    // 原因：bsp_lcd_draw_bitmap函数可能不存在或接口不匹配
    //
    // 正确的做法应该是：
    // 1. 使用LVGL的lv_img_set_src()函数将图片数据设置到GUI控件
    // 2. 或者使用LCD驱动的具体绘图函数
    //
    // 当前代码的问题：
    // - 图片数据读取成功，但没有传递给GUI界面显示
    // - 缺少与LVGL控件的连接

    ESP_LOGW(TAG_FLOWER, "⚠️  图片数据已读取但未显示: %s (%dx%d)", image_name, width, height);
    ESP_LOGW(TAG_FLOWER, "⚠️  需要实现GUI控件更新逻辑");

    free(image_buffer);
    return ESP_OK;
}

/**
 * @brief 更新GUI界面显示花朵信息
 * @details 将花朵数据更新到LVGL界面控件中显示
 * @param flower 花朵信息结构体指针
 * @return esp_err_t
 *         - ESP_OK: 更新成功
 *         - ESP_FAIL: 更新失败
 */
esp_err_t update_gui_flower_display(const flower_info_t* flower)
{
    if (!flower) {
        ESP_LOGE(TAG_FLOWER, "花朵信息指针为空");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG_FLOWER, "开始更新GUI界面显示");

    // 更新花朵名称标签 (第25行：screen_label_name)
    if (guider_ui.screen_label_name) {
        lv_label_set_text(guider_ui.screen_label_name, flower->name);
        ESP_LOGI(TAG_FLOWER, "✓ 更新花朵名称: %s", flower->name);
    }

    // 更新等级标签 (第117行：screen_label_grade)
    if (guider_ui.screen_label_grade) {
        char level_text[32];
        snprintf(level_text, sizeof(level_text), "等级: %d", flower->level);
        lv_label_set_text(guider_ui.screen_label_grade, level_text);
        ESP_LOGI(TAG_FLOWER, "✓ 更新花朵等级: %d", flower->level);
    }

    // 更新成长值标签 (第70行：screen_label_grow)
    if (guider_ui.screen_label_grow) {
        char growth_text[32];
        snprintf(growth_text, sizeof(growth_text), "成长: %d%%", flower->growth);
        lv_label_set_text(guider_ui.screen_label_grow, growth_text);
        ESP_LOGI(TAG_FLOWER, "✓ 更新成长值: %d%%", flower->growth);
    }

    // 更新浇水值标签 (第93行：screen_label_1)
    if (guider_ui.screen_label_1) {
        char water_text[32];
        snprintf(water_text, sizeof(water_text), "水分: %d%%", flower->water);
        lv_label_set_text(guider_ui.screen_label_1, water_text);
        ESP_LOGI(TAG_FLOWER, "✓ 更新水分值: %d%%", flower->water);
    }

    // 🔥 关键：更新花朵图片 (第34行：screen_img_1)
    if (guider_ui.screen_img_1 && strlen(flower->image) > 0) {
        // 读取RGB565图片数据
        uint8_t* image_buffer = NULL;
        size_t image_size = 0;

        esp_err_t ret = read_flower_image(flower->image, &image_buffer, &image_size);
        if (ret == ESP_OK) {
            // 创建LVGL图片描述符
            static lv_img_dsc_t img_dsc;
            img_dsc.header.always_zero = 0;
            img_dsc.header.w = 150;  // 图片宽度
            img_dsc.header.h = 150;  // 图片高度
            img_dsc.data_size = image_size;
            img_dsc.header.cf = LV_IMG_CF_TRUE_COLOR;  // RGB565格式
            img_dsc.data = image_buffer;

            // 设置图片到控件
            lv_img_set_src(guider_ui.screen_img_1, &img_dsc);
            ESP_LOGI(TAG_FLOWER, "✓ 更新花朵图片: %s (%zu bytes)", flower->image, image_size);

            // 注意：image_buffer不能立即释放，LVGL需要持续访问
            // 应该在下次更新图片时再释放上一张图片的内存
        } else {
            ESP_LOGE(TAG_FLOWER, "✗ 花朵图片读取失败: %s", flower->image);
        }
    }

    ESP_LOGI(TAG_FLOWER, "GUI界面更新完成");
    return ESP_OK;
}

/**
 * @brief 扫描并读取所有花朵信息
 * @param flowers 花朵信息数组
 * @param max_count 最大花朵数量
 * @param actual_count 实际读取的花朵数量
 * @return esp_err_t
 */
esp_err_t scan_all_flowers(flower_info_t* flowers, int max_count, int* actual_count)
{
    const char* data_dir = "/sdcard/flower/data";
    DIR* dir = opendir(data_dir);
    if (!dir) {
        ESP_LOGE(TAG_FLOWER, "无法打开花朵数据目录: %s", data_dir);
        return ESP_FAIL;
    }

    *actual_count = 0;
    struct dirent* entry;
    
    while ((entry = readdir(dir)) != NULL && *actual_count < max_count) {
        // 只处理.json文件
        if (strstr(entry->d_name, ".json") == NULL) {
            continue;
        }

        char file_path[512];
        snprintf(file_path, sizeof(file_path), "%s/%s", data_dir, entry->d_name);

        if (read_flower_from_json(file_path, &flowers[*actual_count]) == ESP_OK) {
            (*actual_count)++;
        }
    }

    closedir(dir);
    ESP_LOGI(TAG_FLOWER, "扫描完成，共找到 %d 朵花", *actual_count);
    return ESP_OK;
}

/**
 * @brief 花朵数据管理任务
 * @details 负责扫描SD卡中的花朵数据，并更新到GUI界面显示
 * @param pvParameters 任务参数 (未使用)
 *
 * 🔄 任务执行流程：
 * 1. 等待SD卡挂载完成
 * 2. 扫描/sdcard/flower/data/目录下的所有JSON文件
 * 3. 解析花朵信息并存储到内存
 * 4. 将第一朵花的信息更新到GUI界面显示
 * 5. 任务完成后自动删除
 */
void flower_data_task(void *pvParameters)
{
    ESP_LOGI(TAG_FLOWER, "花朵数据管理任务启动");
    
    // 等待SD卡挂载完成
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 定义花朵数组
    const int MAX_FLOWERS = 10;
    flower_info_t flowers[MAX_FLOWERS];
    int flower_count = 0;
    
    // 扫描所有花朵
    esp_err_t ret = scan_all_flowers(flowers, MAX_FLOWERS, &flower_count);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG_FLOWER, "花朵扫描失败");
        vTaskDelete(NULL);
        return;
    }
    
    // 显示花朵信息
    for (int i = 0; i < flower_count; i++) {
        ESP_LOGI(TAG_FLOWER, "花朵 %d: %s (等级:%d, 成长:%d%%, 水分:%d%%)", 
                 flowers[i].id, flowers[i].name, flowers[i].level, 
                 flowers[i].growth, flowers[i].water);
        
        // 🔥 关键修复：将花朵数据更新到GUI界面
        ESP_LOGI(TAG_FLOWER, "正在更新第 %d 朵花到GUI界面...", i + 1);

        // 更新GUI显示 (只显示第一朵花，后续可扩展为多花朵切换)
        if (i == 0) {
            esp_err_t gui_ret = update_gui_flower_display(&flowers[i]);
            if (gui_ret == ESP_OK) {
                ESP_LOGI(TAG_FLOWER, "✅ 第一朵花已成功显示到GUI界面");
            } else {
                ESP_LOGE(TAG_FLOWER, "❌ GUI界面更新失败");
            }
        }

        // 延时，避免过快处理导致系统不稳定
        vTaskDelay(pdMS_TO_TICKS(100));
    }
    
    // 任务完成
    ESP_LOGI(TAG_FLOWER, "花朵数据处理完成");
    vTaskDelete(NULL);
}

/**
 * @brief 应用程序主函数
 * @details ESP32应用程序入口点，负责系统初始化和任务创建
 *
 * 🚀 系统启动流程：
 * 1. 启动SD卡初始化任务 (优先级5)
 * 2. 启动GUI界面系统 (LCD + LVGL + UI)
 * 3. 启动花朵数据管理任务 (优先级4)
 * 4. 进入主循环，定期喂狗防止看门狗复位
 *
 * ⚠️  注意事项：
 * - 必须先启动GUI系统，再启动花朵数据任务
 * - 花朵数据任务依赖GUI控件已经创建完成
 * - 主循环中必须定期调用esp_task_wdt_reset()
 */
void app_main(void)
{
    ESP_LOGI(TAG_APP, "🚀 ESP32花朵显示系统启动中...");

    // 步骤1: 启动SD卡初始化任务
    ESP_LOGI(TAG_APP, "📱 步骤1: 启动SD卡初始化任务");
    xTaskCreate(sd_card_task, "sd_card_task", 4096, NULL, 5, NULL);
    vTaskDelay(pdMS_TO_TICKS(1000));  // 等待SD卡挂载完成
    ESP_LOGI(TAG_APP, "✅ SD卡挂载完成");

    // 步骤2: 启动GUI界面系统 (必须在花朵数据任务之前)
    ESP_LOGI(TAG_APP, "🖥️  步骤2: 启动GUI界面系统");
    printf("============ 启动GUI界面系统 =============\n");
    esp_err_t ret = start_gui_guider_ui();
    if (ret != ESP_OK) {
        printf("❌ GUI界面启动失败: %s\n", esp_err_to_name(ret));
        printf("⚠️  系统将继续运行，但无GUI界面\n");
    } else {
        printf("✅ GUI界面启动完成，系统已准备就绪！\n");
    }

    // 步骤3: 启动花朵数据管理任务 (依赖GUI已初始化)
    ESP_LOGI(TAG_APP, "🌸 步骤3: 启动花朵数据管理任务");
    xTaskCreate(flower_data_task, "flower_data_task", 8192, NULL, 4, NULL);

    // 步骤4: 进入主循环
    ESP_LOGI(TAG_APP, "🔄 进入主循环，系统运行中...");
    while(1) {
        // 看门狗喂狗 - 防止看门狗复位 (必须要有)
        esp_task_wdt_reset();
        vTaskDelay(pdMS_TO_TICKS(1000));
    }
}