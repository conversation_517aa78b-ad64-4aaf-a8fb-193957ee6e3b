/**
 * @file bsp_lcd.c
 * @brief TFT201A LCD驱动实现
 * @details 基于ESP-IDF esp_lcd组件的ST7789驱动实现
 * <AUTHOR>
 * @date 2024
 */

#include "bsp_lcd.h"
#include "bsp_key.h"        // 引入按键模块接口，用于获取按键事件
#include "esp_task_wdt.h"   // 看门狗驱动

#if LCD_BACKLIGHT_PWM
#include "driver/ledc.h"    // PWM控制驱动
#endif

/*********************************************************************
 * 本地变量和定义
 */
static const char *TAG = "BSP_LCD";                 // 日志标签
static bsp_lcd_t s_lcd = {0};                       // LCD设备结构体实例

// 亮度管理相关定义和变量
#define BRIGHTNESS_MIN 20                   // 最小亮度（20%，避免过暗）
#define BRIGHTNESS_MAX 100                  // 最大亮度（100%）
#define BRIGHTNESS_STEP 20                  // 每次调节步长（20%）
#define BRIGHTNESS_DEFAULT 80               // 默认亮度（80%）

#if LCD_BACKLIGHT_PWM
// PWM背光控制相关定义
#define PWM_TIMER_NUM       LEDC_TIMER_0    // PWM定时器编号
#define PWM_CHANNEL_NUM     LEDC_CHANNEL_0  // PWM通道编号
#define PWM_SPEED_MODE      LEDC_LOW_SPEED_MODE  // PWM速度模式
#define PWM_FREQUENCY       5000            // PWM频率 (5kHz)
#define PWM_RESOLUTION      LEDC_TIMER_8_BIT // PWM分辨率 (8位，0-255)
#define PWM_DUTY_MAX        255             // 最大占空比值

static bool s_pwm_initialized = false;     // PWM初始化状态
#endif

static uint8_t s_current_brightness = BRIGHTNESS_DEFAULT;  // 当前亮度值

// LVGL相关变量
static lv_disp_drv_t s_disp_drv;                    // LVGL显示驱动结构体
static lv_disp_draw_buf_t s_draw_buf_dsc;           // LVGL显示缓冲区描述符
static esp_timer_handle_t s_lvgl_tick_timer = NULL; // LVGL时钟定时器句柄
static bool s_lvgl_initialized = false;             // LVGL初始化状态标志

// LVGL输入设备相关变量
static lv_indev_drv_t s_indev_drv;                  // LVGL输入设备驱动结构体
static lv_indev_t *s_indev_keypad = NULL;           // LVGL按键输入设备句柄
static bool s_input_device_registered = false;      // 输入设备注册状态标志

/*********************************************************************
 * 本地函数声明
 */

static esp_err_t lcd_gpio_init(void);               // GPIO引脚初始化
static esp_err_t lcd_spi_init(void);                // SPI总线初始化
static esp_err_t lcd_panel_init(void);              // LCD面板初始化
static esp_err_t lcd_set_rotation(uint8_t rotation); // 设置LCD旋转方向

// LVGL输入设备相关函数声明
static void keypad_read_callback(lv_indev_drv_t *drv, lv_indev_data_t *data);  // 按键输入回调函数

/*********************************************************************
 * 公共函数实现
 */

/**
 * @brief LCD模块初始化
 * @details 按顺序初始化GPIO、SPI总线和LCD面板
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - ESP_FAIL: 初始化失败
 */
esp_err_t bsp_lcd_init(void)
{
    esp_err_t ret = ESP_OK;

    ESP_LOGI(TAG, "初始化TFT201A LCD驱动");

    // 检查是否已经初始化，避免重复初始化
    if (s_lcd.is_initialized) {
        ESP_LOGW(TAG, "LCD已经初始化");
        return ESP_OK;
    }

    // 1. GPIO引脚初始化（背光、复位等控制引脚）
    ret = lcd_gpio_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "GPIO初始化失败");
        return ret;
    }
    
    // 2. SPI总线初始化
    ret = lcd_spi_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SPI初始化失败");
        return ret;
    }
    
    // 3. LCD面板初始化
    ret = lcd_panel_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD面板初始化失败");
        return ret;
    }
    
    // 4. 设置LCD参数
    s_lcd.width = LCD_H_RES;
    s_lcd.height = LCD_V_RES;
    s_lcd.is_initialized = true;
    
    // 5. 开启背光
    bsp_lcd_backlight_set(80);  // 80%亮度
    
    // 6. 清屏测试
    bsp_lcd_fill_screen(LCD_COLOR_BLACK);
    
    ESP_LOGI(TAG, "LCD初始化完成 - 分辨率: %dx%d", s_lcd.width, s_lcd.height);
    
    return ESP_OK;
}

/**
 * @brief LCD背光控制
 * @details 控制LCD背光的开关，目前为简单的数字开关控制
 * @param level 背光亮度等级 (0-100)，0为关闭，>0为开启
 * @return esp_err_t
 *         - ESP_OK: 设置成功
 * @note 当前实现为简单开关控制，后续可升级为PWM调光
 */
esp_err_t bsp_lcd_backlight_set(uint8_t level)
{
    // 限制亮度范围在0-100之间
    if (level > 100) {
        level = 100;
    }

    // 更新当前亮度值
    s_current_brightness = level;

#if LCD_BACKLIGHT_PWM
    // PWM调光模式
    // 检查PWM是否已初始化
    if (!s_pwm_initialized) {
        ESP_LOGE(TAG, "PWM未初始化，无法设置背光亮度");
        return ESP_ERR_INVALID_STATE;
    }

    // 计算PWM占空比 (0-255)
    uint32_t duty = (level * PWM_DUTY_MAX) / 100;

    // 设置PWM占空比
    esp_err_t ret = ledc_set_duty(PWM_SPEED_MODE, PWM_CHANNEL_NUM, duty);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置PWM占空比失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 更新PWM输出
    ret = ledc_update_duty(PWM_SPEED_MODE, PWM_CHANNEL_NUM);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "更新PWM输出失败: %s", esp_err_to_name(ret));
        return ret;
    }

    if (level > 0) {
        ESP_LOGI(TAG, "背光亮度设置为: %d%% (PWM占空比: %lu/%d)", level, duty, PWM_DUTY_MAX);
    } else {
        ESP_LOGI(TAG, "背光关闭");
    }

#else
    // GPIO开关模式
    if (level > 0) {
        gpio_set_level(LCD_PIN_NUM_BK_LIGHT, 1);  // 拉高背光引脚，开启背光
        ESP_LOGI(TAG, "背光开启 - 亮度: %d%%", level);
    } else {
        gpio_set_level(LCD_PIN_NUM_BK_LIGHT, 0);  // 拉低背光引脚，关闭背光
        ESP_LOGI(TAG, "背光关闭");
    }
#endif

    return ESP_OK;
}

/**
 * @brief 获取当前背光亮度
 * @return uint8_t 当前亮度值 (20-100)
 */
uint8_t bsp_lcd_backlight_get_current(void)
{
    return s_current_brightness;
}

/**
 * @brief 增加背光亮度
 * @details 按步长增加亮度，达到最大值时保持不变
 * @return esp_err_t ESP_OK: 设置成功
 */
esp_err_t bsp_lcd_backlight_increase(void)
{
    uint8_t new_brightness = s_current_brightness + BRIGHTNESS_STEP;

    // 限制最大亮度
    if (new_brightness > BRIGHTNESS_MAX) {
        new_brightness = BRIGHTNESS_MAX;
    }

    ESP_LOGI(TAG, "亮度增加: %d%% -> %d%%", s_current_brightness, new_brightness);
    s_current_brightness = new_brightness;
    return bsp_lcd_backlight_set(new_brightness);
}

/**
 * @brief 降低背光亮度
 * @details 按步长降低亮度，达到最小值时保持不变
 * @return esp_err_t ESP_OK: 设置成功
 */
esp_err_t bsp_lcd_backlight_decrease(void)
{
    uint8_t new_brightness;

    // 防止下溢
    if (s_current_brightness <= BRIGHTNESS_MIN + BRIGHTNESS_STEP) {
        new_brightness = BRIGHTNESS_MIN;
    } else {
        new_brightness = s_current_brightness - BRIGHTNESS_STEP;
    }

    ESP_LOGI(TAG, "亮度降低: %d%% -> %d%%", s_current_brightness, new_brightness);
    s_current_brightness = new_brightness;
    return bsp_lcd_backlight_set(new_brightness);
}

/**
 * @brief 清屏（全屏填充指定颜色）
 * @details 使用指定颜色填充整个LCD屏幕
 * @param color 填充颜色，RGB565格式 (如0xF800=红色, 0x07E0=绿色, 0x001F=蓝色)
 * @return esp_err_t
 *         - ESP_OK: 填充成功
 *         - ESP_ERR_INVALID_STATE: LCD未初始化
 */
esp_err_t bsp_lcd_fill_screen(uint16_t color)
{
    // 检查LCD是否已初始化
    if (!s_lcd.is_initialized) {
        ESP_LOGE(TAG, "LCD未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    // 调用矩形填充函数，填充整个屏幕区域
    return bsp_lcd_fill_rect(0, 0, s_lcd.width - 1, s_lcd.height - 1, color);
}

/**
 * @brief 在指定矩形区域填充颜色
 * @details 在LCD屏幕的指定矩形区域内填充单一颜色
 * @param x_start 起始X坐标 (0 ~ width-1)
 * @param y_start 起始Y坐标 (0 ~ height-1)
 * @param x_end   结束X坐标 (x_start ~ width-1)
 * @param y_end   结束Y坐标 (y_start ~ height-1)
 * @param color   填充颜色，RGB565格式
 * @return esp_err_t
 *         - ESP_OK: 填充成功
 *         - ESP_ERR_INVALID_STATE: LCD未初始化
 *         - ESP_ERR_INVALID_ARG: 坐标参数错误
 *         - ESP_ERR_NO_MEM: 内存分配失败
 */
esp_err_t bsp_lcd_fill_rect(uint16_t x_start, uint16_t y_start,
                           uint16_t x_end, uint16_t y_end, uint16_t color)
{
    // 检查LCD是否已初始化
    if (!s_lcd.is_initialized) {
        ESP_LOGE(TAG, "LCD未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    // 坐标参数有效性检查
    if (x_start >= s_lcd.width || y_start >= s_lcd.height ||
        x_end >= s_lcd.width || y_end >= s_lcd.height ||
        x_start > x_end || y_start > y_end) {
        ESP_LOGE(TAG, "坐标参数错误");
        return ESP_ERR_INVALID_ARG;
    }

    // 计算填充区域的尺寸
    uint16_t width = x_end - x_start + 1;
    uint16_t height = y_end - y_start + 1;
    uint32_t pixel_count = width * height;

    // 动态分配颜色缓冲区（每个像素2字节RGB565）
    uint16_t *color_buffer = malloc(pixel_count * sizeof(uint16_t));
    if (color_buffer == NULL) {
        ESP_LOGE(TAG, "内存分配失败");//执行这句，但是无所谓
        return ESP_ERR_NO_MEM;
    }

    // 用指定颜色填充缓冲区
    for (uint32_t i = 0; i < pixel_count; i++) {
        color_buffer[i] = color;
    }

    // 通过ESP-IDF LCD API将颜色数据发送到LCD控制器
    esp_err_t ret = esp_lcd_panel_draw_bitmap(s_lcd.panel_handle,
                                             x_start, y_start,
                                             x_end + 1, y_end + 1,
                                             color_buffer);

    // 释放临时分配的内存
    free(color_buffer);
    
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD绘制失败: %s", esp_err_to_name(ret));
    }
    
    return ret;
}

/**
 * @brief 显示位图数据（内部函数）
 * @details 主要供LVGL使用，一般用户使用fill_rect和fill_screen即可
 */
static esp_err_t bsp_lcd_draw_bitmap(uint16_t x_start, uint16_t y_start,
                                    uint16_t x_end, uint16_t y_end,
                                    const uint16_t *color_data)
{
    if (!s_lcd.is_initialized) {
        ESP_LOGE(TAG, "LCD未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    if (color_data == NULL) {
        ESP_LOGE(TAG, "颜色数据指针为空");
        return ESP_ERR_INVALID_ARG;
    }
    
    // 参数检查
    if (x_start >= s_lcd.width || y_start >= s_lcd.height ||
        x_end >= s_lcd.width || y_end >= s_lcd.height ||
        x_start > x_end || y_start > y_end) {
        ESP_LOGE(TAG, "坐标参数错误");
        return ESP_ERR_INVALID_ARG;
    }
    
    return esp_lcd_panel_draw_bitmap(s_lcd.panel_handle, 
                                    x_start, y_start, 
                                    x_end + 1, y_end + 1, 
                                    color_data);
}

/**
 * @brief 画点（内部函数）
 * @details 一般用户使用fill_rect即可，此函数主要用于特殊需求
 */
static esp_err_t bsp_lcd_draw_pixel(uint16_t x, uint16_t y, uint16_t color)
{
    return bsp_lcd_fill_rect(x, y, x, y, color);
}

/**
 * @brief 获取LCD句柄（内部函数）
 * @details 主要供内部模块使用
 */
static bsp_lcd_t* bsp_lcd_get_handle(void)
{
    return &s_lcd;
}

/**
 * @brief LCD睡眠模式控制（内部函数）
 * @details 一般应用较少使用，主要用于低功耗场景
 * @note 预留函数，当前未使用
 */
__attribute__((unused)) static esp_err_t bsp_lcd_sleep(bool sleep)
{
    if (!s_lcd.is_initialized) {
        ESP_LOGE(TAG, "LCD未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    esp_err_t ret;
    
    if (sleep) {
        ret = esp_lcd_panel_disp_on_off(s_lcd.panel_handle, false);
        ESP_LOGI(TAG, "LCD进入睡眠模式");
    } else {
        ret = esp_lcd_panel_disp_on_off(s_lcd.panel_handle, true);
        ESP_LOGI(TAG, "LCD退出睡眠模式");
    }
    
    return ret;
}

/**
 * @brief RGB888转RGB565
 */
uint16_t bsp_lcd_color_rgb565(uint8_t r, uint8_t g, uint8_t b)
{
    return ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
}

/*********************************************************************
 * LVGL集成函数实现
 */

/**
 * @brief LVGL时钟回调函数
 * @param data 时钟周期参数
 */
static void lvgl_tick_inc_cb(void *data)
{
    uint32_t tick_inc_period_ms = *((uint32_t *) data);
    lv_tick_inc(tick_inc_period_ms);
}



/**
 * @brief LVGL显示刷新函数
 * @param disp_drv 显示驱动指针
 * @param area 刷新区域
 * @param color_p 颜色数据指针
 */
static void lvgl_disp_flush(lv_disp_drv_t *disp_drv, const lv_area_t *area, lv_color_t *color_p)
{
    //这里加位偏移，直接会花屏
    // 调用底层LCD驱动显示位图
    esp_err_t ret = bsp_lcd_draw_bitmap(area->x1, area->y1, area->x2, area->y2,
                                       (const uint16_t*)color_p);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LVGL显示刷新失败");
    }

    // 通知LVGL刷新完成
    lv_disp_flush_ready(disp_drv);
}

/**
 * @brief 获取默认LVGL配置
 */
bsp_lvgl_config_t bsp_lvgl_get_default_config(void)
{
    bsp_lvgl_config_t config = {
        .buffer_height = 80,        // 80行缓冲区（增加缓冲区大小）
        .double_buffer = true,      // 启用双缓冲（提高性能）
    };
    return config;
}

/**
 * @brief 初始化LVGL显示驱动
 */
esp_err_t bsp_lvgl_init(const bsp_lvgl_config_t *config)
{
    if (s_lvgl_initialized) {
        ESP_LOGW(TAG, "LVGL已经初始化");
        return ESP_OK;
    }

    if (!s_lcd.is_initialized) {
        ESP_LOGE(TAG, "LCD未初始化，请先调用bsp_lcd_init()");
        return ESP_ERR_INVALID_STATE;
    }

    // 使用默认配置
    bsp_lvgl_config_t default_config = bsp_lvgl_get_default_config();
    if (config == NULL) {
        config = &default_config;
    }

    ESP_LOGI(TAG, "开始初始化LVGL...");

    // 1. 初始化LVGL库
    lv_init();

    // 2. 分配显示缓冲区内存(必须使用DMA内存)
    size_t buffer_size = LCD_H_RES * config->buffer_height * sizeof(lv_color_t);
    lv_color_t *buf1 = heap_caps_malloc(buffer_size, MALLOC_CAP_INTERNAL | MALLOC_CAP_DMA);
    lv_color_t *buf2 = NULL;

    if (config->double_buffer) {
        buf2 = heap_caps_malloc(buffer_size, MALLOC_CAP_INTERNAL | MALLOC_CAP_DMA);
    }

    ESP_LOGI(TAG, "分配显示缓冲区: %dx%lu, 大小:%u字节, 双缓冲:%s",
             LCD_H_RES, (unsigned long)config->buffer_height, (unsigned int)buffer_size,
             config->double_buffer ? "是" : "否");

    if (buf1 == NULL || (config->double_buffer && buf2 == NULL)) {
        ESP_LOGE(TAG, "LVGL显示缓冲区内存分配失败");
        if (buf1) free(buf1);
        if (buf2) free(buf2);
        return ESP_ERR_NO_MEM;
    }

    // 3. 初始化显示缓冲区
    lv_disp_draw_buf_init(&s_draw_buf_dsc, buf1, buf2, LCD_H_RES * config->buffer_height);

    // 4. 初始化显示驱动
    lv_disp_drv_init(&s_disp_drv);
    s_disp_drv.hor_res = LCD_H_RES;
    s_disp_drv.ver_res = LCD_V_RES;
    s_disp_drv.flush_cb = lvgl_disp_flush;
    s_disp_drv.draw_buf = &s_draw_buf_dsc;

    // 显示偏移设置，这里改了也没用
    s_disp_drv.offset_x = 0;  // X轴偏移，正值向右移动
    s_disp_drv.offset_y = 0;  // Y轴偏移，正值向下移动

    // 5. 注册显示驱动
    lv_disp_drv_register(&s_disp_drv);

    // 6. 创建LVGL时钟定时器（固定5ms周期）
    static uint32_t tick_period = 5;  // 固定5ms时钟周期

    const esp_timer_create_args_t timer_args = {
        .callback = lvgl_tick_inc_cb,
        .name = "lvgl_tick",
        .arg = &tick_period,
        .dispatch_method = ESP_TIMER_TASK,
        .skip_unhandled_events = true,
    };

    esp_err_t ret = esp_timer_create(&timer_args, &s_lvgl_tick_timer);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LVGL定时器创建失败");
        return ret;
    }

    ret = esp_timer_start_periodic(s_lvgl_tick_timer, tick_period * 1000);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LVGL定时器启动失败");
        esp_timer_delete(s_lvgl_tick_timer);
        return ret;
    }

    s_lvgl_initialized = true;
    ESP_LOGI(TAG, "LVGL初始化完成 - 分辨率:%dx%d, 缓冲区:%lu行",
             LCD_H_RES, LCD_V_RES, (unsigned long)config->buffer_height);

    return ESP_OK;
}

/**
 * @brief LVGL任务处理函数
 */
void bsp_lvgl_task_handler(uint32_t delay_ms)
{
    if (!s_lvgl_initialized) {
        ESP_LOGW(TAG, "LVGL未初始化");
        return;
    }

    lv_timer_handler();

    if (delay_ms > 0) {
        vTaskDelay(pdMS_TO_TICKS(delay_ms));
    }
}

/**
 * @brief LVGL处理任务
 * @param pvParam 任务参数(未使用)
 */
static void lvgl_task(void *pvParam)
{
    ESP_LOGI(TAG, "LVGL处理任务启动");

    // 将当前任务注册到看门狗监控
    esp_err_t ret = esp_task_wdt_add(NULL);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "LVGL任务已注册到看门狗监控");
    } else if (ret == ESP_ERR_INVALID_ARG) {
        ESP_LOGI(TAG, "LVGL任务已在看门狗监控中");
    } else {
        ESP_LOGW(TAG, "LVGL任务注册看门狗失败: %s", esp_err_to_name(ret));
    }

    while (1) {
        // 直接调用lv_timer_handler，获取建议的下次调用时间
        uint32_t time_till_next = lv_timer_handler();

        // 喂狗，防止看门狗复位（只有注册成功才喂狗）
        if (ret == ESP_OK || ret == ESP_ERR_INVALID_ARG) {
            esp_task_wdt_reset();
        }

        // 使用LVGL建议的延时时间，但最少5ms，最多20ms
        uint32_t delay = time_till_next;
        if (delay < 5) delay = 5;       // 最少5ms，避免CPU占用过高
        if (delay > 20) delay = 20;     // 最多20ms，保证响应性

        vTaskDelay(pdMS_TO_TICKS(delay));
    }
}

/**
 * @brief 创建LVGL处理任务
 */
esp_err_t bsp_lvgl_create_task(uint32_t task_priority, uint32_t stack_size)
{
    if (!s_lvgl_initialized) {
        ESP_LOGE(TAG, "LVGL未初始化，请先调用bsp_lvgl_init()");
        return ESP_ERR_INVALID_STATE;
    }

    // 设置默认参数
    if (task_priority == 0) {
        task_priority = 4;  // 默认优先级
    }
    if (stack_size == 0) {
        stack_size = 4096;  // 默认栈大小
    }

    BaseType_t ret = xTaskCreate(
        lvgl_task,
        "lvgl_task",
        stack_size,
        NULL,
        task_priority,
        NULL
    );

    if (ret != pdPASS) {
        ESP_LOGE(TAG, "LVGL任务创建失败");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "LVGL任务创建成功 - 优先级:%lu, 栈大小:%lu", task_priority, stack_size);
    return ESP_OK;
}

/*********************************************************************
 * 本地函数实现
 */

#if LCD_BACKLIGHT_PWM
/**
 * @brief PWM背光控制初始化
 * @details 初始化PWM用于背光亮度控制
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - 其他: PWM配置失败
 */
static esp_err_t lcd_pwm_init(void)
{
    ESP_LOGI(TAG, "初始化PWM背光控制");

    // 配置PWM定时器
    ledc_timer_config_t timer_config = {
        .speed_mode = PWM_SPEED_MODE,
        .timer_num = PWM_TIMER_NUM,
        .duty_resolution = PWM_RESOLUTION,
        .freq_hz = PWM_FREQUENCY,
        .clk_cfg = LEDC_AUTO_CLK
    };

    esp_err_t ret = ledc_timer_config(&timer_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "PWM定时器配置失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 配置PWM通道
    ledc_channel_config_t channel_config = {
        .channel = PWM_CHANNEL_NUM,
        .duty = 0,                              // 初始占空比为0（关闭背光）
        .gpio_num = LCD_PIN_NUM_BK_LIGHT,
        .speed_mode = PWM_SPEED_MODE,
        .timer_sel = PWM_TIMER_NUM,
        .intr_type = LEDC_INTR_DISABLE
    };

    ret = ledc_channel_config(&channel_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "PWM通道配置失败: %s", esp_err_to_name(ret));
        return ret;
    }

    s_pwm_initialized = true;
    ESP_LOGI(TAG, "PWM背光控制初始化完成 - 频率:%dHz, 分辨率:%d位",
             PWM_FREQUENCY, (1 << PWM_RESOLUTION) - 1);
    return ESP_OK;
}
#endif

/**
 * @brief GPIO引脚初始化
 * @details 根据配置选择PWM调光或GPIO开关控制背光
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - 其他: 初始化失败
 */
static esp_err_t lcd_gpio_init(void)
{
    ESP_LOGI(TAG, "初始化LCD控制引脚");

#if LCD_BACKLIGHT_PWM
    // 使用PWM控制背光
    esp_err_t ret = lcd_pwm_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "PWM背光控制初始化失败");
        return ret;
    }
#else
    // 使用GPIO开关控制背光
    gpio_config_t bk_gpio_config = {
        .mode = GPIO_MODE_OUTPUT,                           // 输出模式
        .pin_bit_mask = 1ULL << LCD_PIN_NUM_BK_LIGHT,      // 背光引脚位掩码
        .pull_down_en = GPIO_PULLDOWN_DISABLE,             // 禁用下拉电阻
        .pull_up_en = GPIO_PULLUP_DISABLE,                 // 禁用上拉电阻
        .intr_type = GPIO_INTR_DISABLE,                    // 禁用中断
    };

    esp_err_t ret = gpio_config(&bk_gpio_config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "背光GPIO配置失败");
        return ret;
    }

    // 初始状态设置为关闭背光（低电平）
    gpio_set_level(LCD_PIN_NUM_BK_LIGHT, 0);
#endif

    ESP_LOGI(TAG, "LCD控制引脚初始化完成");
    return ESP_OK;
}

/**
 * @brief SPI总线初始化
 * @details 初始化与LCD通信的SPI总线，配置时钟、数据引脚和DMA
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - 其他: SPI总线初始化失败
 */
static esp_err_t lcd_spi_init(void)
{
    ESP_LOGI(TAG, "初始化SPI总线");

    // SPI总线配置结构体
    spi_bus_config_t buscfg = {
        .sclk_io_num = LCD_PIN_NUM_CLK,                     // SPI时钟引脚
        .mosi_io_num = LCD_PIN_NUM_MOSI,                    // SPI数据输出引脚
        .miso_io_num = -1,                                  // LCD不需要MISO（数据输入）
        .quadwp_io_num = -1,                                // 不使用四线SPI
        .quadhd_io_num = -1,                                // 不使用四线SPI
        .max_transfer_sz = LCD_H_RES * LCD_V_RES * sizeof(uint16_t), // 最大传输大小（全屏数据）
    };

    // 初始化SPI总线，使用自动DMA通道分配
    esp_err_t ret = spi_bus_initialize(LCD_HOST, &buscfg, SPI_DMA_CH_AUTO);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SPI总线初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "SPI总线初始化完成");
    return ESP_OK;
}

/**
 * @brief LCD面板初始化
 */
static esp_err_t lcd_panel_init(void)
{
    ESP_LOGI(TAG, "初始化LCD面板");

    esp_err_t ret;

    // LCD面板IO配置
    esp_lcd_panel_io_spi_config_t io_config = {
        .dc_gpio_num = LCD_PIN_NUM_DC,
        .cs_gpio_num = LCD_PIN_NUM_CS,
        .pclk_hz = LCD_PIXEL_CLOCK_HZ,
        .lcd_cmd_bits = 8,
        .lcd_param_bits = 8,
        .spi_mode = 0,
        .trans_queue_depth = 10,
    };

    // 创建LCD面板IO句柄
    ret = esp_lcd_new_panel_io_spi((esp_lcd_spi_bus_handle_t)LCD_HOST,
                                   &io_config, &s_lcd.io_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建LCD IO句柄失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // LCD面板配置
    esp_lcd_panel_dev_config_t panel_config = {
        .reset_gpio_num = LCD_PIN_NUM_RST,
        .rgb_ele_order = LCD_RGB_ELEMENT_ORDER_BGR,  // ST7789通常使用BGR
        .bits_per_pixel = LCD_BIT_PER_PIXEL,
    };

    // 创建ST7789面板句柄
    ret = esp_lcd_new_panel_st7789(s_lcd.io_handle, &panel_config, &s_lcd.panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建ST7789面板失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 添加显示偏移设置（参考您的参考代码需要的偏移）
    ret = esp_lcd_panel_set_gap(s_lcd.panel_handle, 24, 0);  // X偏移24，Y偏移0；这样就对了
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "设置显示偏移失败: %s", esp_err_to_name(ret));
    } else {
        ESP_LOGI(TAG, "设置显示偏移: X=24, Y=0");
    }

    // 复位LCD面板
    ret = esp_lcd_panel_reset(s_lcd.panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD面板复位失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 初始化LCD面板
    ret = esp_lcd_panel_init(s_lcd.panel_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD面板初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 参考旧st7789_driver代码，添加额外的初始化命令
    ESP_LOGI(TAG, "发送额外的ST7789初始化命令...");

    // 颜色反转命令（参考旧代码第122行）
    ret = esp_lcd_panel_io_tx_param(s_lcd.io_handle, 0x20, NULL, 0);  // INVOFF - 颜色反转关闭
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "发送INVOFF命令失败: %s", esp_err_to_name(ret));
    }

    // 普通显示模式（参考旧代码第123行）
    ret = esp_lcd_panel_io_tx_param(s_lcd.io_handle, 0x13, NULL, 0);  // NORON - 普通显示模式
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "发送NORON命令失败: %s", esp_err_to_name(ret));
    }

    // 伽马校正（参考旧代码第151-152行）
    uint8_t gamma_pos[] = {0xD0, 0x08, 0x11, 0x08, 0x08, 0x15, 0x39, 0x54, 0x50, 0x1C, 0x17, 0x17, 0x27, 0x30};
    ret = esp_lcd_panel_io_tx_param(s_lcd.io_handle, 0xE0, gamma_pos, sizeof(gamma_pos));
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "发送正伽马校正命令失败: %s", esp_err_to_name(ret));
    }

    uint8_t gamma_neg[] = {0xD0, 0x08, 0x11, 0x08, 0x08, 0x15, 0x39, 0x54, 0x50, 0x1C, 0x17, 0x17, 0x27, 0x30};
    ret = esp_lcd_panel_io_tx_param(s_lcd.io_handle, 0xE1, gamma_neg, sizeof(gamma_neg));
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "发送负伽马校正命令失败: %s", esp_err_to_name(ret));
    }

    ESP_LOGI(TAG, "额外ST7789初始化命令发送完成");

    // 设置LCD旋转方向（使用简洁的旋转配置）
    ret = lcd_set_rotation(LCD_ROTATION);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置LCD旋转失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 显示偏移修正已移除，使用标准显示方式

    // 开启显示
    ret = esp_lcd_panel_disp_on_off(s_lcd.panel_handle, true);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "开启LCD显示失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "LCD面板初始化完成");
    return ESP_OK;
}

/**
 * @brief 设置LCD旋转方向
 * @details 参考st7789_driver的简洁旋转配置方式，通过修改bsp_lcd.h中的LCD_ROTATION宏定义即可适应不同使用场景
 *
 * 使用方法：
 * 1. 在bsp_lcd.h中修改 #define LCD_ROTATION 值
 * 2. 重新编译即可自动应用新的旋转方向和分辨率
 *
 * 旋转选项说明：
 * - LCD_ROTATION = 0: 竖屏模式，240×320，不旋转（默认方向）
 * - LCD_ROTATION = 1: 横屏模式，320×240，顺时针旋转90°（推荐横屏使用）
 * - LCD_ROTATION = 2: 倒置竖屏，240×320，旋转180°
 * - LCD_ROTATION = 3: 倒置横屏，320×240，顺时针旋转270°（逆时针90°）
 *
 * 自动功能：
 * - 分辨率自动调整：横屏时自动变为320×240，竖屏时为240×320
 * - LVGL界面自动适配：根据旋转方向选择合适的布局
 * - 镜像参数自动配置：无需手动调整swap_xy和mirror参数
 *
 * @param rotation 旋转方向 (0=不旋转, 1=顺时针90°, 2=180°, 3=顺时针270°)
 * @return esp_err_t
 *         - ESP_OK: 设置成功
 *         - 其他: 设置失败
 */
static esp_err_t lcd_set_rotation(uint8_t rotation)
{
    esp_err_t ret = ESP_OK;
    bool swap_xy = false;
    bool mirror_x = false;
    bool mirror_y = false;

    ESP_LOGI(TAG, "设置LCD旋转方向: %d", rotation);

    // 根据旋转方向设置swap_xy和mirror参数（参考st7789_driver的MADCTL寄存器逻辑）
    // 这些参数组合经过实际测试，确保显示方向正确
    switch (rotation) {
        case 0:  // 不旋转 (竖屏，240×320，0°) - 默认方向
            swap_xy = false;    // 不交换X/Y轴
            mirror_x = false;   // X轴不镜像
            mirror_y = false;   // Y轴不镜像
            break;

        case 1:  // 顺时针90° (横屏，320×240) - 推荐横屏方向
            swap_xy = true;     // 交换X/Y轴实现90°旋转
            mirror_x = true;    // X轴镜像修正显示方向
            mirror_y = true;    // Y轴镜像修正显示方向
            break;

        case 2:  // 180° (倒置竖屏，240×320) - 倒置显示
            swap_xy = false;    // 不交换X/Y轴，保持竖屏比例
            mirror_x = false;   // X轴不镜像
            mirror_y = true;    // 仅Y轴镜像实现180°倒置
            break;

        case 3:  // 顺时针270° (倒置横屏，320×240，逆时针90°)
            swap_xy = true;     // 交换X/Y轴实现90°旋转
            mirror_x = false;   // X轴不镜像
            mirror_y = false;   // Y轴不镜像
            break;

        default:
            ESP_LOGW(TAG, "无效的旋转参数: %d，使用默认值0", rotation);
            // 默认为竖屏不旋转
            swap_xy = false;
            mirror_x = false;
            mirror_y = false;
            break;
    }

    // 第一步：应用XY轴交换设置
    // swap_xy=true时，X轴和Y轴坐标互换，实现90°/270°旋转的基础
    ret = esp_lcd_panel_swap_xy(s_lcd.panel_handle, swap_xy);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置XY轴交换失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 第二步：应用镜像设置
    // mirror_x: X轴镜像（左右翻转）
    // mirror_y: Y轴镜像（上下翻转）
    // 通过镜像组合修正旋转后的显示方向
    ret = esp_lcd_panel_mirror(s_lcd.panel_handle, mirror_x, mirror_y);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "设置镜像失败: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "LCD旋转设置完成 - swap_xy:%d, mirror_x:%d, mirror_y:%d",
             swap_xy, mirror_x, mirror_y);

    return ESP_OK;
}

/*********************************************************************
 * LVGL输入设备相关函数实现
 */

/**
 * @brief LVGL按键输入设备回调函数 - 输入驱动的核心
 * @details 这是LVGL输入系统的关键函数，体现了LVGL输入驱动的核心原理：
 *
 * 【LVGL输入驱动原理】：
 * 1. 轮询机制：LVGL每5-10ms调用一次此函数（在lv_timer_handler中）
 * 2. 状态查询：函数返回当前输入设备的瞬时状态，而不是事件
 * 3. 事件生成：LVGL比较前后两次状态，自动生成按下/释放事件
 * 4. 事件分发：生成的事件被发送给当前焦点对象处理
 *
 * 【为什么这样设计】：
 * - 统一接口：所有输入设备都用相同的回调模式，便于管理
 * - 线程安全：在LVGL主线程中调用，避免并发问题
 * - 实时性好：高频调用确保输入响应及时
 * - 易于调试：可以在回调中添加日志跟踪输入状态
 *
 * @param drv  输入设备驱动指针（可用于获取设备特定配置）
 * @param data 输出参数：LVGL输入数据结构，函数需要填充当前输入状态
 * @note 此函数在LVGL任务上下文中调用，必须快速返回（<1ms）
 */
static void keypad_read_callback(lv_indev_drv_t *drv, lv_indev_data_t *data)
{
    /*
     * 静态变量用于状态维持
     * 原理：由于LVGL期望获取"当前状态"而不是"事件"，
     * 我们需要将事件转换为状态，并在按键释放前保持状态
     */
    static key_event_t last_key_event = {0};
    key_event_t key_event;

    /*
     * 核心逻辑1：从硬件抽象层获取输入数据
     *
     * 设计原理：
     * - 使用非阻塞调用（超时0ms），确保不影响LVGL刷新性能
     * - 通过队列机制与中断解耦，避免在回调中直接处理中断
     * - 保持模块间低耦合：LCD模块只调用Key模块的标准接口
     */
    esp_err_t ret = bsp_key_get_event(&key_event, 0);

    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "=== LVGL按键回调被调用 ===");
        ESP_LOGI(TAG, "获取到按键事件: key=%d, event=%d", key_event.key, key_event.event);
        /*
         * 核心逻辑2：事件到状态的转换
         *
         * 关键概念理解：
         * - 硬件层产生的是"事件"（按下、释放、短按、长按）
         * - LVGL需要的是"状态"（当前是否按下 + 按键码）
         * - 我们需要将瞬时事件转换为持续状态
         *
         * 状态转换逻辑：
         * 1. 按下事件 -> 设置为按下状态，记录按键码
         * 2. 释放事件 -> 设置为释放状态
         * 3. 无事件时 -> 保持释放状态（防止按键"粘住"）
         */

        // 只处理短按和长按事件，避免重复触发
        // 原理：短按代表一次完整的按键操作，按下事件只是中间状态
        if (key_event.event == KEY_EVENT_SHORT_PRESS ||
            key_event.event == KEY_EVENT_LONG_PRESS) {

            /*
             * 设置LVGL输入状态为按下
             * 原理：LVGL通过state字段判断输入设备当前状态
             * - LV_INDEV_STATE_PRESSED: 按下状态
             * - LV_INDEV_STATE_RELEASED: 释放状态
             */
            data->state = LV_INDEV_STATE_PRESSED;

            /*
             * 核心逻辑3：按键码映射
             *
             * 映射策略说明：
             * 物理按键 -> LVGL按键码 -> UI行为
             * TC0 -> LV_KEY_LEFT -> 向左导航/减少数值
             * TC1 -> LV_KEY_ENTER (短按) / LV_KEY_ESC (长按) -> 确认选择/全局菜单
             * TC2 -> LV_KEY_RIGHT -> 向右导航/增加数值
             *
             * 长按特殊处理：
             * - TC1长按映射为LV_KEY_ESC，用于全局菜单功能
             * - 其他按键长按保持原有映射
             */
            switch (key_event.key) {
                case TOUCH_KEY_TC0:
                    data->key = LV_KEY_LEFT;
                    ESP_LOGI(TAG, "LVGL按键映射: TC0 -> LEFT (%s)",
                             key_event.event == KEY_EVENT_LONG_PRESS ? "长按" : "短按");
                    break;

                case TOUCH_KEY_TC1:
                    // 【关键修复】TC1只处理短按，长按由长按监控任务处理
                    if (key_event.event == KEY_EVENT_SHORT_PRESS) {
                        data->key = LV_KEY_ENTER;  // 只有短按才映射为ENTER
                        ESP_LOGI(TAG, "LVGL按键映射: TC1 -> ENTER (短按)");
                    } else {
                        // 长按事件不传递给LVGL，由长按监控任务专门处理
                        data->state = LV_INDEV_STATE_RELEASED;  // 强制设为释放状态
                        ESP_LOGI(TAG, "TC1长按事件忽略，由长按监控任务处理");
                        return;  // 直接返回，不设置按键码
                    }
                    break;

                case TOUCH_KEY_TC2:
                    data->key = LV_KEY_RIGHT;
                    ESP_LOGI(TAG, "LVGL按键映射: TC2 -> RIGHT (%s)",
                             key_event.event == KEY_EVENT_LONG_PRESS ? "长按" : "短按");
                    break;

                default:
                    data->key = LV_KEY_ENTER;       // 安全默认值
                    ESP_LOGI(TAG, "LVGL按键映射: 未知按键%d -> ENTER", key_event.key);
                    break;
            }

            // 保存事件用于状态维持（如果需要的话）
            last_key_event = key_event;

        }

    } else {
        /*
         * 核心逻辑4：无事件时的状态管理
         *
         * 重要原理：
         * - LVGL期望每次调用都返回当前状态
         * - 当没有新事件时，应该返回"释放"状态
         * - 这防止了按键"粘住"在按下状态的问题
         *
         * 为什么不保持上次的按下状态？
         * - 我们的按键是瞬时触发的，不是持续按下的
         * - 保持按下状态会导致连续的按键事件
         * - 释放状态是安全的默认状态
         */
        data->state = LV_INDEV_STATE_RELEASED;
    }
}

/**
 * @brief 注册LVGL按键输入设备
 * @details 创建并注册LVGL按键输入设备，将外部物理按键集成到LVGL输入系统
 *          注册后，物理按键事件将自动转换为LVGL按键事件，可用于界面导航
 * @return esp_err_t
 *         - ESP_OK: 注册成功
 *         - ESP_ERR_INVALID_STATE: LVGL未初始化或输入设备已注册
 *         - ESP_FAIL: 注册失败
 * @note 必须在bsp_lvgl_init()成功调用之后使用
 *       建议在启动UI界面之前调用此函数
 */
esp_err_t bsp_lcd_register_input_device(void)
{
    // 检查LVGL是否已经初始化
    if (!s_lvgl_initialized) {
        ESP_LOGE(TAG, "LVGL未初始化，无法注册输入设备");
        return ESP_ERR_INVALID_STATE;
    }

    // 检查输入设备是否已经注册，避免重复注册
    if (s_input_device_registered) {
        ESP_LOGW(TAG, "输入设备已经注册");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "开始注册LVGL按键输入设备...");

    /*
     * 步骤1：初始化输入设备驱动结构体
     * 原理：lv_indev_drv_t是LVGL定义的输入设备驱动描述符
     * 作用：告诉LVGL这个输入设备的特性和如何与它通信
     */
    lv_indev_drv_init(&s_indev_drv);

    /*
     * 步骤2：设置输入设备类型
     * 原理：LVGL根据设备类型采用不同的事件处理策略
     *
     * 设备类型说明：
     * - LV_INDEV_TYPE_POINTER: 指针设备（触摸屏、鼠标）- 产生坐标事件
     * - LV_INDEV_TYPE_KEYPAD:  按键设备（键盘、按钮）- 产生按键码事件
     * - LV_INDEV_TYPE_BUTTON:  单按钮设备 - 映射到特定UI对象
     * - LV_INDEV_TYPE_ENCODER: 编码器设备（旋转编码器）- 产生增量事件
     *
     * 我们选择KEYPAD类型是因为：
     * 1. 支持多个按键的组合使用
     * 2. 可以产生方向键事件（LEFT/RIGHT/UP/DOWN）
     * 3. 支持确认键（ENTER）和取消键（ESC）
     * 4. 适合菜单导航和界面操作
     */
    s_indev_drv.type = LV_INDEV_TYPE_KEYPAD;

    /*
     * 步骤3：设置读取回调函数
     * 原理：LVGL采用"拉取"模式获取输入数据
     *
     * 回调机制工作原理：
     * 1. LVGL在每个刷新周期（通常5-10ms）调用此函数
     * 2. 函数需要快速返回当前输入状态（按下/释放 + 按键码）
     * 3. LVGL根据返回的数据生成相应的输入事件
     * 4. 事件被分发给当前焦点的UI对象处理
     *
     * 为什么不用中断直接推送事件？
     * - LVGL需要在自己的时间节拍上处理事件，确保线程安全
     * - 回调方式可以进行事件过滤和预处理
     * - 支持连续按键和重复事件的处理
     */
    s_indev_drv.read_cb = keypad_read_callback;

    /*
     * 步骤4：向LVGL注册输入设备
     * 原理：LVGL维护一个输入设备链表，定期遍历调用每个设备的回调
     *
     * 注册过程：
     * 1. LVGL分配一个lv_indev_t结构体实例
     * 2. 将我们的驱动配置复制到实例中
     * 3. 将实例添加到全局输入设备链表
     * 4. 返回设备句柄供后续操作使用
     *
     * 注册后LVGL会：
     * - 在每个刷新周期调用read_cb获取输入状态
     * - 根据设备类型和返回数据生成输入事件
     * - 将事件发送给当前活动的屏幕和对象
     */
    s_indev_keypad = lv_indev_drv_register(&s_indev_drv);

    // 检查注册结果
    if (s_indev_keypad == NULL) {
        ESP_LOGE(TAG, "LVGL输入设备注册失败");
        return ESP_FAIL;
    }

    /*
     * 步骤5：关联输入设备到输入组
     * 原理：LVGL的按键事件需要通过输入组系统才能正确分发
     *
     * 输入组的作用：
     * 1. 管理焦点对象：决定按键事件发送给哪个UI对象
     * 2. 按键导航：支持TAB键在对象间切换焦点
     * 3. 事件分发：将按键事件路由到正确的处理函数
     *
     * 为什么需要输入组？
     * - 没有输入组，按键事件只能发送到屏幕级别
     * - 有了输入组，可以精确控制哪个对象接收按键
     * - 支持复杂的焦点管理和导航逻辑
     */
    lv_group_t *default_group = lv_group_get_default();
    if (default_group == NULL) {
        // 如果没有默认输入组，创建一个
        default_group = lv_group_create();
        lv_group_set_default(default_group);
        ESP_LOGI(TAG, "创建默认输入组");
    }

    // 将输入设备关联到输入组
    lv_indev_set_group(s_indev_keypad, default_group);
    ESP_LOGI(TAG, "输入设备已关联到输入组");

    // 标记输入设备已注册
    s_input_device_registered = true;

    ESP_LOGI(TAG, "LVGL按键输入设备注册成功");
    ESP_LOGI(TAG, "按键映射: TC0->LEFT, TC1->ENTER, TC2->RIGHT");

    return ESP_OK;
}

/*********************************************************************
 * LVGL移植层完整初始化函数实现
 */

/**
 * @brief LVGL移植层完整初始化函数
 * @details 提供一站式的LCD和LVGL初始化服务，简化main.c的调用
 *          按照正确的顺序执行所有必要的初始化步骤
 * @return esp_err_t 初始化结果
 */
esp_err_t lv_port_init(void)
{
    esp_err_t ret;

    ESP_LOGI(TAG, "开始LVGL移植层完整初始化...");

    /*
     * 步骤1: LCD硬件驱动初始化
     * 包括：GPIO配置、SPI总线初始化、ST7789面板初始化、屏幕旋转设置
     */
    ESP_LOGI(TAG, "步骤1: 初始化LCD硬件驱动");
    ret = bsp_lcd_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD硬件驱动初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "LCD硬件驱动初始化完成");

    /*
     * 步骤2: LVGL库和显示驱动初始化
     * 包括：LVGL库初始化、显示缓冲区配置、显示驱动注册、定时器设置
     */
    ESP_LOGI(TAG, "步骤2: 初始化LVGL库和显示驱动");
    ret = bsp_lvgl_init(NULL);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LVGL库和显示驱动初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "LVGL库和显示驱动初始化完成");

    /*
     * 步骤3: 注册LVGL输入设备
     * 将外部按键集成到LVGL输入系统，支持界面按键操作
     * 注意：这一步需要Key模块已经初始化完成
     */
    ESP_LOGI(TAG, "步骤3: 注册LVGL输入设备");
    ret = bsp_lcd_register_input_device();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LVGL输入设备注册失败: %s", esp_err_to_name(ret));
        // 输入设备注册失败不是致命错误，可以继续运行（只是无法使用按键）
        ESP_LOGW(TAG, "继续初始化，但按键功能将不可用");
    } else {
        ESP_LOGI(TAG, "LVGL输入设备注册完成");
    }

    /*
     * 步骤4: 创建LVGL处理任务
     * 创建后台任务自动处理LVGL刷新，无需手动调用lv_timer_handler
     */
    ESP_LOGI(TAG, "步骤4: 创建LVGL处理任务");
    ret = bsp_lvgl_create_task(4, 4096);  // 优先级4，栈大小4KB
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LVGL处理任务创建失败: %s", esp_err_to_name(ret));
        return ret;
    }
    ESP_LOGI(TAG, "LVGL处理任务创建完成");

    /*
     * 初始化完成总结
     */
    ESP_LOGI(TAG, "=== LVGL移植层初始化完成 ===");
    ESP_LOGI(TAG, "LCD分辨率: 320x240 (横屏)");
    ESP_LOGI(TAG, "显示驱动: ST7789");
    ESP_LOGI(TAG, "输入设备: 3个触摸按键 (TC0/TC1/TC2)");
    ESP_LOGI(TAG, "按键映射: TC0→LEFT, TC1→ENTER, TC2→RIGHT");
    ESP_LOGI(TAG, "LVGL任务: 优先级4, 栈大小4KB");
    ESP_LOGI(TAG, "系统已准备就绪，可以加载UI界面");

    return ESP_OK;
}


