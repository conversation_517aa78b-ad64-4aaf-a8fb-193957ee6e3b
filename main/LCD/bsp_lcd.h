/**
 * @file bsp_lcd.h
 * @brief TFT201A LCD驱动头文件
 * @details 基于ESP-IDF esp_lcd组件的ST7789驱动实现
 *          支持2.0寸TFT LCD显示屏的基本显示功能
 * <AUTHOR>
 * @date 2024
 */

#ifndef __BSP_LCD_H__
#define __BSP_LCD_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "esp_lcd_panel_io.h"
#include "esp_lcd_panel_vendor.h"
#include "esp_lcd_panel_ops.h"
#include "driver/gpio.h"
#include "driver/spi_master.h"
#include "esp_err.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_heap_caps.h"
#include "lvgl.h"

/*********************************************************************
 * LCD硬件引脚定义 - TFT201A LCD连接配置
 */
#define LCD_HOST            SPI2_HOST           // SPI主机控制器（SPI2）
#define LCD_PIXEL_CLOCK_HZ  (10 * 1000 * 1000) // SPI时钟频率：10MHz（降低频率提高稳定性）

// LCD控制引脚定义（根据硬件原理图配置）
#define LCD_PIN_NUM_MOSI    40                  // SPI主输出从输入引脚（数据传输）
#define LCD_PIN_NUM_CLK     41                  // SPI时钟引脚
#define LCD_PIN_NUM_CS      0                   // SPI片选引脚（低电平有效）
#define LCD_PIN_NUM_DC      39                  // 数据/命令选择引脚（D/C）
#define LCD_PIN_NUM_RST     (-1)                // 复位引脚（未使用，依赖硬件复位）
#define LCD_PIN_NUM_BK_LIGHT 42                 // 背光控制引脚（高电平开启）

/*********************************************************************
 * LCD显示参数配置
 */
// 背光控制方式选择
#define LCD_BACKLIGHT_PWM   1                   // 1=使用PWM调光, 0=使用GPIO开关控制
                                                // PWM调光：支持真正的亮度调节(20%-100%)
                                                // GPIO控制：只支持开关(亮/灭)

// 屏幕旋转配置（参考st7789_driver的简洁方式）
#define LCD_ROTATION        1                   // 0=不旋转, 1=顺时针90°, 2=180°, 3=顺时针270°

// 显示偏移配置（用于修正ST7789显示位置）
#define LCD_OFFSET_X        24                  // X方向偏移像素数（修正左移问题）
#define LCD_OFFSET_Y        0                   // Y方向偏移像素数

// 屏幕分辨率定义（根据旋转自动调整）
#if (LCD_ROTATION == 1 || LCD_ROTATION == 3)
    #define LCD_H_RES       296                 // 横屏模式：水平分辨率
    #define LCD_V_RES       240                 // 横屏模式：垂直分辨率
#else
    #define LCD_H_RES       240                 // 竖屏模式：水平分辨率
    #define LCD_V_RES       296                 // 竖屏模式：垂直分辨率
#endif

#define LCD_BIT_PER_PIXEL   16                  // 色深：16位RGB565格式

// 常用颜色定义（RGB565格式：5位红+6位绿+5位蓝）
#define LCD_COLOR_BLACK     0x0000              // 黑色
#define LCD_COLOR_WHITE     0xFFFF              // 白色
#define LCD_COLOR_RED       0xF800              // 红色
#define LCD_COLOR_GREEN     0x07E0              // 绿色
#define LCD_COLOR_BLUE      0x001F              // 蓝色
#define LCD_COLOR_YELLOW    0xFFE0              // 黄色（红+绿）
#define LCD_COLOR_CYAN      0x07FF              // 青色（绿+蓝）
#define LCD_COLOR_MAGENTA   0xF81F              // 洋红色（红+蓝）

/*********************************************************************
 * LCD驱动结构体定义
 */
typedef struct {
    esp_lcd_panel_handle_t panel_handle;       // LCD面板操作句柄
    esp_lcd_panel_io_handle_t io_handle;       // LCD IO通信句柄
    bool is_initialized;                       // 初始化完成标志
    uint16_t width;                            // 屏幕像素宽度
    uint16_t height;                           // 屏幕像素高度
} bsp_lcd_t;

/*********************************************************************
 * LVGL配置结构体（简化版）
 */
typedef struct {
    uint32_t buffer_height;                    // 显示缓冲区高度(行数，推荐值: 40)
    bool double_buffer;                        // 是否使用双缓冲(推荐: false)
} bsp_lvgl_config_t;

/*********************************************************************
 * 核心接口函数声明 - 对外提供的主要API
 *
 * 使用说明：
 * 1. 旋转配置：修改LCD_ROTATION宏定义 (0=竖屏, 1=横屏, 2=倒置竖屏, 3=倒置横屏)
 * 2. 基本使用：bsp_lcd_init() -> bsp_lcd_backlight_set() -> bsp_lcd_fill_screen()
 * 3. LVGL集成：bsp_lcd_init() -> bsp_lvgl_init() -> bsp_lvgl_create_task()
 * 4. 颜色格式：使用预定义颜色(LCD_COLOR_*)或bsp_lcd_color_rgb565()转换
 */

/**
 * @brief LCD模块初始化
 * @details 一键初始化LCD驱动，包括SPI、控制器、旋转方向等所有配置
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - ESP_FAIL: 初始化失败
 * @note 使用前必须先调用此函数
 */
esp_err_t bsp_lcd_init(void);

/**
 * @brief LCD背光控制
 * @details 控制LCD背光开关和亮度
 * @param level 背光亮度等级 (0-100)
 *              0: 关闭背光
 *              1-100: 背光亮度（当前为开关控制）
 * @return esp_err_t
 *         - ESP_OK: 设置成功
 */
esp_err_t bsp_lcd_backlight_set(uint8_t level);

/**
 * @brief 获取当前背光亮度
 * @return uint8_t 当前亮度值 (20-100)
 */
uint8_t bsp_lcd_backlight_get_current(void);

/**
 * @brief 增加背光亮度
 * @details 按步长增加亮度，达到最大值时保持不变
 * @return esp_err_t ESP_OK: 设置成功
 */
esp_err_t bsp_lcd_backlight_increase(void);

/**
 * @brief 降低背光亮度
 * @details 按步长降低亮度，达到最小值时保持不变
 * @return esp_err_t ESP_OK: 设置成功
 */
esp_err_t bsp_lcd_backlight_decrease(void);

/**
 * @brief 全屏填充颜色
 * @details 用指定颜色清屏或填充整个显示区域
 * @param color 填充颜色 (RGB565格式，如LCD_COLOR_BLACK)
 * @return esp_err_t
 *         - ESP_OK: 操作成功
 */
esp_err_t bsp_lcd_fill_screen(uint16_t color);

/**
 * @brief 矩形区域填充颜色
 * @details 在指定矩形区域内填充单一颜色
 * @param x_start 起始X坐标 (0 ~ LCD_H_RES-1)
 * @param y_start 起始Y坐标 (0 ~ LCD_V_RES-1)
 * @param x_end   结束X坐标 (x_start ~ LCD_H_RES-1)
 * @param y_end   结束Y坐标 (y_start ~ LCD_V_RES-1)
 * @param color   填充颜色 (RGB565格式)
 * @return esp_err_t
 *         - ESP_OK: 操作成功
 */
esp_err_t bsp_lcd_fill_rect(uint16_t x_start, uint16_t y_start,
                           uint16_t x_end, uint16_t y_end, uint16_t color);

/**
 * @brief RGB888转RGB565颜色格式
 * @details 将常用的RGB888格式转换为LCD使用的RGB565格式
 * @param r 红色分量 (0-255)
 * @param g 绿色分量 (0-255)
 * @param b 蓝色分量 (0-255)
 * @return uint16_t RGB565格式的颜色值
 * @example bsp_lcd_color_rgb565(255, 0, 0) 返回红色
 */
uint16_t bsp_lcd_color_rgb565(uint8_t r, uint8_t g, uint8_t b);

/*********************************************************************
 * LVGL集成接口 - 图形界面支持
 */

/**
 * @brief 初始化LVGL图形库
 * @details 一键初始化LVGL与LCD的集成，创建显示驱动和缓冲区
 * @param config LVGL配置参数，传NULL使用默认配置
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - ESP_FAIL: 初始化失败
 * @note 需要先调用bsp_lcd_init()
 */
esp_err_t bsp_lvgl_init(const bsp_lvgl_config_t *config);

/**
 * @brief 创建LVGL处理任务
 * @details 创建后台任务自动处理LVGL刷新，无需手动调用处理函数
 * @param task_priority 任务优先级 (推荐值: 4)
 * @param stack_size    任务栈大小 (推荐值: 4096)
 * @return esp_err_t
 *         - ESP_OK: 创建成功
 *         - ESP_FAIL: 创建失败
 */
esp_err_t bsp_lvgl_create_task(uint32_t task_priority, uint32_t stack_size);

/**
 * @brief 注册LVGL按键输入设备
 * @details 将外部按键集成到LVGL输入系统中，支持界面按键操作
 *          通过调用Key模块接口获取按键事件，转换为LVGL按键码
 *          支持TC0/TC1/TC2三个按键的导航和确认操作
 * @return esp_err_t
 *         - ESP_OK: 注册成功
 *         - ESP_FAIL: 注册失败
 * @note 必须在bsp_lvgl_init()之后调用
 *       需要Key模块已经初始化完成
 */
esp_err_t bsp_lcd_register_input_device(void);

/**
 * @brief LVGL移植层完整初始化函数
 * @details 一站式初始化函数，包含LCD和LVGL的完整初始化流程：
 *          1. ST7789 LCD驱动初始化
 *          2. LVGL库初始化
 *          3. LVGL显示驱动注册
 *          4. LVGL输入设备注册
 *          5. LVGL处理任务创建
 * @return esp_err_t
 *         - ESP_OK: 初始化成功
 *         - ESP_FAIL: 初始化失败
 * @note 这是main.c中唯一需要调用的LCD/LVGL初始化函数
 *       调用前需要确保Key模块已经初始化（用于输入设备注册）
 */
esp_err_t lv_port_init(void);

#ifdef __cplusplus
}
#endif

#endif /* __BSP_LCD_H__ */
