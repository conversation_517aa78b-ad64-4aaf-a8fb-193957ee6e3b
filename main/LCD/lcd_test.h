/**
 * @file lcd_test.h
 * @brief LCD驱动测试程序头文件
 * @details 提供LCD和LVGL测试接口
 */

#ifndef __LCD_TEST_H__
#define __LCD_TEST_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "esp_err.h"

/**
 * @brief 启动LVGL演示
 * @details 一键初始化LCD、LVGL并创建演示界面
 * @return esp_err_t
 *         - ESP_OK: 启动成功
 *         - ESP_FAIL: 启动失败
 */
esp_err_t start_lvgl_demo(void);

/**
 * @brief 启动GUI Guider生成的UI
 * @details 一键初始化LCD、LVGL并加载GUI Guider生成的界面
 * @return esp_err_t
 *         - ESP_OK: 启动成功
 *         - ESP_FAIL: 启动失败
 */
esp_err_t start_gui_guider_ui(void);

#ifdef __cplusplus
}
#endif

#endif /* __LCD_TEST_H__ */
