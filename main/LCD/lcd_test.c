/**
 * @file lcd_test.c
 * @brief LCD驱动测试程序
 * @details 提供LCD功能测试和演示
 * <AUTHOR>
 * @date 2024
 */

#include "bsp_lcd.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "ui/generated/gui_guider.h"
#include "ui/generated/events_init.h"
#include "ui/custom/custom.h"
#include "esp_log.h"
#include "esp_task_wdt.h"
#include "esp_heap_caps.h"
#include "esp_timer.h"
#include "esp_system.h"

static const char *TAG = "LCD_TEST";

// GUI Guider全局UI实例
lv_ui guider_ui;

/**
 * @brief LCD基本功能测试
 * @details 测试清屏、填充矩形、画点等基本功能
 */
void lcd_basic_test(void)
{
    ESP_LOGI(TAG, "开始LCD基本功能测试");
    
    // 1. 红色清屏
    ESP_LOGI(TAG, "测试1: 红色清屏");
    bsp_lcd_fill_screen(LCD_COLOR_RED);
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 2. 绿色清屏
    ESP_LOGI(TAG, "测试2: 绿色清屏");
    bsp_lcd_fill_screen(LCD_COLOR_GREEN);
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 3. 蓝色清屏
    ESP_LOGI(TAG, "测试3: 蓝色清屏");
    bsp_lcd_fill_screen(LCD_COLOR_BLUE);
    vTaskDelay(pdMS_TO_TICKS(1000));
    
    // 4. 黑色清屏
    ESP_LOGI(TAG, "测试4: 黑色清屏");
    bsp_lcd_fill_screen(LCD_COLOR_BLACK);
    vTaskDelay(pdMS_TO_TICKS(500));
    
    // 5. 绘制彩色矩形
    ESP_LOGI(TAG, "测试5: 绘制彩色矩形");
    bsp_lcd_fill_rect(50, 50, 100, 100, LCD_COLOR_RED);      // 红色矩形
    bsp_lcd_fill_rect(120, 50, 170, 100, LCD_COLOR_GREEN);   // 绿色矩形
    bsp_lcd_fill_rect(50, 120, 100, 170, LCD_COLOR_BLUE);    // 蓝色矩形
    bsp_lcd_fill_rect(120, 120, 170, 170, LCD_COLOR_YELLOW); // 黄色矩形
    vTaskDelay(pdMS_TO_TICKS(2000));
    
    // 6. 画点测试（使用1x1矩形代替）
    ESP_LOGI(TAG, "测试6: 画点测试");
    bsp_lcd_fill_screen(LCD_COLOR_BLACK);
    for (int i = 0; i < 100; i++) {
        uint16_t x = rand() % LCD_H_RES;
        uint16_t y = rand() % LCD_V_RES;
        uint16_t color = rand() & 0xFFFF;
        bsp_lcd_fill_rect(x, y, x, y, color);  // 使用1x1矩形代替画点
        vTaskDelay(pdMS_TO_TICKS(10));
    }
    
    ESP_LOGI(TAG, "LCD基本功能测试完成");
}

/**
 * @brief LCD颜色测试
 * @details 测试RGB565颜色转换和显示
 */
void lcd_color_test(void)
{
    ESP_LOGI(TAG, "开始LCD颜色测试");
    
    // 渐变色测试
    bsp_lcd_fill_screen(LCD_COLOR_BLACK);
    
    // 红色渐变
    for (int i = 0; i < 32; i++) {
        uint16_t color = bsp_lcd_color_rgb565(i * 8, 0, 0);
        bsp_lcd_fill_rect(i * 7, 50, i * 7 + 6, 80, color);
    }
    
    // 绿色渐变
    for (int i = 0; i < 32; i++) {
        uint16_t color = bsp_lcd_color_rgb565(0, i * 8, 0);
        bsp_lcd_fill_rect(i * 7, 100, i * 7 + 6, 130, color);
    }
    
    // 蓝色渐变
    for (int i = 0; i < 32; i++) {
        uint16_t color = bsp_lcd_color_rgb565(0, 0, i * 8);
        bsp_lcd_fill_rect(i * 7, 150, i * 7 + 6, 180, color);
    }
    
    vTaskDelay(pdMS_TO_TICKS(3000));
    
    ESP_LOGI(TAG, "LCD颜色测试完成");
}

/**
 * @brief LCD背光测试
 * @details 测试背光控制功能
 */
void lcd_backlight_test(void)
{
    ESP_LOGI(TAG, "开始LCD背光测试");
    
    bsp_lcd_fill_screen(LCD_COLOR_WHITE);
    
    // 背光开关测试
    for (int i = 0; i < 5; i++) {
        ESP_LOGI(TAG, "关闭背光");
        bsp_lcd_backlight_set(0);
        vTaskDelay(pdMS_TO_TICKS(500));
        
        ESP_LOGI(TAG, "开启背光");
        bsp_lcd_backlight_set(100);
        vTaskDelay(pdMS_TO_TICKS(500));
    }
    
    ESP_LOGI(TAG, "LCD背光测试完成");
}

/**
 * @brief LCD综合演示
 * @details 综合演示LCD的各种功能
 */
void lcd_demo(void)
{
    ESP_LOGI(TAG, "开始LCD综合演示");
    
    while (1) {
        // 基本功能测试
        lcd_basic_test();
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 颜色测试
        lcd_color_test();
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 背光测试
        lcd_backlight_test();
        vTaskDelay(pdMS_TO_TICKS(1000));
        
        // 循环间隔
        vTaskDelay(pdMS_TO_TICKS(2000));
    }
}

/**
 * @brief LCD测试任务
 * @param pvParameters 任务参数
 */
void lcd_test_task(void *pvParameters)
{
    ESP_LOGI(TAG, "LCD测试任务启动");
    
    // 初始化LCD
    esp_err_t ret = bsp_lcd_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LCD初始化失败: %s", esp_err_to_name(ret));
        vTaskDelete(NULL);
        return;
    }
    
    // 等待一下让LCD稳定
    vTaskDelay(pdMS_TO_TICKS(100));
    
    // 开始演示
    lcd_demo();
}

/**
 * @brief 启动LCD测试
 * @details 创建LCD测试任务
 */
void start_lcd_test(void)
{
    xTaskCreatePinnedToCore(
        lcd_test_task,           // 任务函数
        "lcd_test_task",         // 任务名称
        4096,                    // 栈大小
        NULL,                    // 任务参数
        5,                       // 任务优先级
        NULL,                    // 任务句柄
        1                        // 运行在核心1
    );
}

/**
 * @brief LVGL演示界面创建
 * @details 创建适应横屏/竖屏的LVGL用户界面
 */
static void create_lvgl_demo_ui(void)
{
    // 创建主屏幕
    lv_obj_t *scr = lv_scr_act();
    lv_obj_set_style_bg_color(scr, lv_color_hex(0x003a57), LV_PART_MAIN);

#if (LCD_ROTATION == 1 || LCD_ROTATION == 3)
    // 横屏布局 (320×240) - 居中信息

    // 居中标题
    lv_obj_t *title = lv_label_create(scr);
    lv_label_set_text(title, "ESP32-S3 + LVGL");
    lv_obj_set_style_text_color(title, lv_color_white(), LV_PART_MAIN);
    lv_obj_set_style_text_font(title, &lv_font_montserrat_14, LV_PART_MAIN);
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 15);

    // 居中信息标签
    lv_obj_t *info = lv_label_create(scr);
    lv_label_set_text(info, "TFT201A LCD 320x240\nST7789 LVGL v8.4.0");
    lv_obj_set_style_text_color(info, lv_color_hex(0x00ff00), LV_PART_MAIN);
    lv_obj_set_style_text_align(info, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN);
    lv_obj_align(info, LV_ALIGN_CENTER, 0, -20);

    // 居中按钮
    lv_obj_t *btn = lv_btn_create(scr);
    lv_obj_set_size(btn, 100, 35);
    lv_obj_align(btn, LV_ALIGN_CENTER, 0, 30);
    lv_obj_set_style_bg_color(btn, lv_color_hex(0xff6600), LV_PART_MAIN);

    lv_obj_t *btn_label = lv_label_create(btn);
    lv_label_set_text(btn_label, "BUTTON");
    lv_obj_center(btn_label);

    // 底部进度条
    lv_obj_t *bar = lv_bar_create(scr);
    lv_obj_set_size(bar, 250, 15);
    lv_obj_align(bar, LV_ALIGN_BOTTOM_MID, 0, -40);
    lv_bar_set_value(bar, 70, LV_ANIM_OFF);

    // 进度条标签（在进度条上方居中）
    lv_obj_t *bar_label = lv_label_create(scr);
    lv_label_set_text(bar_label, "Progress: 70%");
    lv_obj_set_style_text_color(bar_label, lv_color_white(), LV_PART_MAIN);
    lv_obj_align_to(bar_label, bar, LV_ALIGN_OUT_BOTTOM_MID, 0, -5);

#else
    // 竖屏布局 (240×320) - 原始布局

    lv_obj_t *title = lv_label_create(scr);
    lv_label_set_text(title, "ESP32-S3 + LVGL");
    lv_obj_set_style_text_color(title, lv_color_white(), LV_PART_MAIN);
    lv_obj_set_style_text_font(title, &lv_font_montserrat_14, LV_PART_MAIN);
    lv_obj_align(title, LV_ALIGN_TOP_MID, 0, 20);

    lv_obj_t *info = lv_label_create(scr);
    lv_label_set_text(info, "TFT201A LCD\n240x320 ST7789\nLVGL v8.4.0");
    lv_obj_set_style_text_color(info, lv_color_hex(0x00ff00), LV_PART_MAIN);
    lv_obj_set_style_text_align(info, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN);
    lv_obj_align(info, LV_ALIGN_CENTER, 0, -20);

    lv_obj_t *btn = lv_btn_create(scr);
    lv_obj_set_size(btn, 120, 50);
    lv_obj_align(btn, LV_ALIGN_BOTTOM_MID, 0, -30);
    lv_obj_set_style_bg_color(btn, lv_color_hex(0xff6600), LV_PART_MAIN);

    lv_obj_t *btn_label = lv_label_create(btn);
    lv_label_set_text(btn_label, "BUTTON");
    lv_obj_center(btn_label);

    lv_obj_t *bar = lv_bar_create(scr);
    lv_obj_set_size(bar, 200, 20);
    lv_obj_align(bar, LV_ALIGN_BOTTOM_MID, 0, -100);
    lv_bar_set_value(bar, 70, LV_ANIM_OFF);

    lv_obj_t *bar_label = lv_label_create(scr);
    lv_label_set_text(bar_label, "status: 70%");
    lv_obj_set_style_text_color(bar_label, lv_color_white(), LV_PART_MAIN);
    lv_obj_align_to(bar_label, bar, LV_ALIGN_OUT_TOP_MID, 0, -10);

#endif

    ESP_LOGI(TAG, "LVGL演示界面创建完成");
}

/**
 * @brief 启动LVGL演示
 */
esp_err_t start_lvgl_demo(void)
{
    ESP_LOGI(TAG, "开始启动LVGL演示...");

    // 1. 确保LCD已初始化
    if (bsp_lcd_init() != ESP_OK) {
        ESP_LOGE(TAG, "LCD初始化失败");
        return ESP_FAIL;
    }

    // 跳过颜色测试，直接初始化LVGL（避免大内存分配）
    ESP_LOGI(TAG, "跳过颜色测试，准备初始化LVGL...");

    // 2. 初始化LVGL
    ESP_LOGI(TAG, "初始化LVGL...");
    esp_err_t ret = bsp_lvgl_init(NULL);  // 使用默认配置
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LVGL初始化失败");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "LVGL初始化成功，创建界面...");

    // 3. 创建演示界面
    create_lvgl_demo_ui();

    ESP_LOGI(TAG, "界面创建完成，启动任务...");

    // 4. 创建LVGL处理任务
    ret = bsp_lvgl_create_task(4, 4096);  // 优先级4, 栈大小4KB
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "LVGL任务创建失败");
        return ESP_FAIL;
    }

    ESP_LOGI(TAG, "LVGL演示启动成功！");
    return ESP_OK;
}


//-----------------------------------------GUI部分---------------------------------------

/*********************************************************************
 * 系统诊断和测试函数
 */

/**
 * @brief 系统内存状态检查
 * @details 检查堆内存使用情况，帮助诊断内存不足问题
 */
static void system_memory_check(void)
{
    ESP_LOGI(TAG, "=== 系统内存状态检查 ===");

    // 总体堆内存信息
    size_t free_heap = esp_get_free_heap_size();
    size_t min_free_heap = esp_get_minimum_free_heap_size();
    size_t total_heap = heap_caps_get_total_size(MALLOC_CAP_DEFAULT);

    ESP_LOGI(TAG, "总堆内存: %lu bytes (%.1f KB)",
             (unsigned long)total_heap, (float)total_heap / 1024.0f);
    ESP_LOGI(TAG, "当前剩余堆内存: %lu bytes (%.1f KB)",
             (unsigned long)free_heap, (float)free_heap / 1024.0f);
    ESP_LOGI(TAG, "历史最小剩余堆内存: %lu bytes (%.1f KB)",
             (unsigned long)min_free_heap, (float)min_free_heap / 1024.0f);
    ESP_LOGI(TAG, "内存使用率: %.1f%%",
             (float)(total_heap - free_heap) * 100.0f / total_heap);

    // DMA内存信息（LVGL缓冲区使用）
    size_t free_dma = heap_caps_get_free_size(MALLOC_CAP_DMA);
    size_t total_dma = heap_caps_get_total_size(MALLOC_CAP_DMA);

    ESP_LOGI(TAG, "DMA内存总量: %lu bytes (%.1f KB)",
             (unsigned long)total_dma, (float)total_dma / 1024.0f);
    ESP_LOGI(TAG, "DMA内存剩余: %lu bytes (%.1f KB)",
             (unsigned long)free_dma, (float)free_dma / 1024.0f);

    // 内存警告检查
    if (free_heap < 50000) {  // 小于50KB
        ESP_LOGW(TAG, "⚠️  剩余堆内存较少，可能影响系统稳定性");
    }
    if (free_dma < 20000) {   // 小于20KB
        ESP_LOGW(TAG, "⚠️  DMA内存不足，可能影响LVGL显示");
    }

    ESP_LOGI(TAG, "========================");
}

/**
 * @brief 看门狗状态检查和配置
 * @details 检查看门狗配置，添加当前任务到看门狗监控
 */
static void watchdog_status_check(void)
{
    ESP_LOGI(TAG, "=== 看门狗状态检查 ===");

    // 将当前任务添加到看门狗监控
    esp_err_t ret = esp_task_wdt_add(NULL);
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "✅ 当前任务已添加到看门狗监控");
    } else if (ret == ESP_ERR_INVALID_ARG) {
        ESP_LOGI(TAG, "ℹ️  当前任务已在看门狗监控中");
    } else {
        ESP_LOGW(TAG, "⚠️  添加任务到看门狗失败: %s", esp_err_to_name(ret));
    }

    // 立即喂狗一次
    esp_task_wdt_reset();
    ESP_LOGI(TAG, "✅ 看门狗已喂狗");

    ESP_LOGI(TAG, "看门狗配置信息:");
    ESP_LOGI(TAG, "- 超时时间: 5秒 (CONFIG_ESP_TASK_WDT_TIMEOUT_S)");
    ESP_LOGI(TAG, "- 监控空闲任务: 是");
    ESP_LOGI(TAG, "- 复位模式: 重启系统");
    ESP_LOGI(TAG, "===================");
}

/**
 * @brief 系统启动时间记录
 * @details 记录关键初始化步骤的耗时，帮助诊断启动慢的问题
 */
static void system_timing_start(const char* step_name, int64_t* start_time)
{
    *start_time = esp_timer_get_time();
    ESP_LOGI(TAG, "⏱️  开始: %s", step_name);
}

static void system_timing_end(const char* step_name, int64_t start_time)
{
    int64_t end_time = esp_timer_get_time();
    int64_t duration_us = end_time - start_time;
    float duration_ms = (float)duration_us / 1000.0f;

    ESP_LOGI(TAG, "✅ 完成: %s (耗时: %.2f ms)", step_name, duration_ms);

    // 如果某个步骤耗时过长，给出警告
    if (duration_ms > 1000.0f) {  // 超过1秒
        ESP_LOGW(TAG, "⚠️  步骤 '%s' 耗时较长，可能导致看门狗超时", step_name);
    }
}

// 测试任务函数
static void test_task_function(void* param)
{
    vTaskDelay(pdMS_TO_TICKS(100));
    vTaskDelete(NULL);
}

// 测试定时器回调函数
static void test_timer_callback(void* arg)
{
    // 空回调，仅用于测试
}

/**
 * @brief 系统稳定性测试
 * @details 在GUI启动前进行系统稳定性检查
 */
static esp_err_t system_stability_test(void)
{
    ESP_LOGI(TAG, "=== 系统稳定性测试 ===");

    // 1. 内存分配测试
    ESP_LOGI(TAG, "测试1: 内存分配稳定性");
    void* test_ptr = malloc(10240);  // 分配10KB
    if (test_ptr) {
        memset(test_ptr, 0xAA, 10240);  // 写入测试数据
        free(test_ptr);
        ESP_LOGI(TAG, "✅ 内存分配测试通过");
    } else {
        ESP_LOGE(TAG, "❌ 内存分配测试失败");
        return ESP_ERR_NO_MEM;
    }

    // 2. 任务创建测试
    ESP_LOGI(TAG, "测试2: 任务创建稳定性");
    TaskHandle_t test_task = NULL;
    BaseType_t ret = xTaskCreate(
        test_task_function,
        "test_task", 1024, NULL, 1, &test_task
    );
    if (ret == pdPASS) {
        ESP_LOGI(TAG, "✅ 任务创建测试通过");
    } else {
        ESP_LOGE(TAG, "❌ 任务创建测试失败");
        return ESP_FAIL;
    }

    // 3. 定时器测试
    ESP_LOGI(TAG, "测试3: 定时器稳定性");
    esp_timer_handle_t test_timer;
    esp_timer_create_args_t timer_args = {
        .callback = test_timer_callback,
        .name = "test_timer"
    };
    esp_err_t timer_ret = esp_timer_create(&timer_args, &test_timer);
    if (timer_ret == ESP_OK) {
        esp_timer_delete(test_timer);
        ESP_LOGI(TAG, "✅ 定时器测试通过");
    } else {
        ESP_LOGE(TAG, "❌ 定时器测试失败: %s", esp_err_to_name(timer_ret));
        return timer_ret;
    }

    ESP_LOGI(TAG, "✅ 系统稳定性测试全部通过");
    ESP_LOGI(TAG, "====================");
    return ESP_OK;
}

/**
 * @brief 启动GUI Guider生成的UI
 * @details 使用lv_port_init进行完整的LCD和LVGL初始化，然后加载GUI界面
 */
esp_err_t start_gui_guider_ui(void)
{
    ESP_LOGI(TAG, "🚀 开始启动GUI Guider UI...");

    int64_t step_time;
    esp_err_t ret;

    // ===== 启动前系统诊断 =====
    ESP_LOGI(TAG, "📊 执行启动前系统诊断...");

    // 1. 系统内存检查
    system_timing_start("系统内存检查", &step_time);
    system_memory_check();
    system_timing_end("系统内存检查", step_time);

    // 2. 看门狗状态检查
    system_timing_start("看门狗状态检查", &step_time);
    watchdog_status_check();
    system_timing_end("看门狗状态检查", step_time);

    // 3. 系统稳定性测试
    system_timing_start("系统稳定性测试", &step_time);
    ret = system_stability_test();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "❌ 系统稳定性测试失败，停止GUI启动");
        return ret;
    }
    system_timing_end("系统稳定性测试", step_time);

    // ===== 开始GUI初始化 =====
    ESP_LOGI(TAG, "🎨 开始GUI系统初始化...");

    // 4. LVGL移植层初始化
    system_timing_start("LVGL移植层初始化", &step_time);
    ESP_LOGI(TAG, "调用lv_port_init进行完整初始化...");
    ret = lv_port_init();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "❌ LVGL移植层初始化失败: %s", esp_err_to_name(ret));
        return ESP_FAIL;
    }
    system_timing_end("LVGL移植层初始化", step_time);

    // 喂狗，防止初始化过程中看门狗超时
    esp_task_wdt_reset();
    ESP_LOGI(TAG, "🐕 看门狗已喂狗 (LVGL初始化后)");

    // 5. 系统稳定等待
    system_timing_start("系统稳定等待", &step_time);
    ESP_LOGI(TAG, "等待LVGL系统稳定...");
    vTaskDelay(pdMS_TO_TICKS(100));
    system_timing_end("系统稳定等待", step_time);

    // 6. GUI界面加载
    system_timing_start("GUI界面加载", &step_time);
    ESP_LOGI(TAG, "开始加载GUI Guider界面...");
    setup_ui(&guider_ui);
    system_timing_end("GUI界面加载", step_time);

    // 再次喂狗
    esp_task_wdt_reset();
    ESP_LOGI(TAG, "🐕 看门狗已喂狗 (GUI界面加载后)");

    // 7. UI按键控制系统初始化
    system_timing_start("UI按键控制系统初始化", &step_time);
    custom_init(&guider_ui);
    system_timing_end("UI按键控制系统初始化", step_time);

    // 8. 事件处理初始化
    system_timing_start("事件处理初始化", &step_time);
    events_init(&guider_ui);
    system_timing_end("事件处理初始化", step_time);

    // ===== 启动后状态检查 =====
    ESP_LOGI(TAG, "📋 执行启动后状态检查...");
    system_memory_check();  // 再次检查内存使用情况

    // 最后一次喂狗
    esp_task_wdt_reset();
    ESP_LOGI(TAG, "🐕 看门狗已喂狗 (启动完成)");

    ESP_LOGI(TAG, "✅ GUI Guider UI启动成功！");
    ESP_LOGI(TAG, "🎯 当前显示: 开机界面，2秒后将自动跳转到主界面");
    ESP_LOGI(TAG, "💡 提示: 如果出现看门狗复位，请检查上述诊断信息");

    return ESP_OK;
}

