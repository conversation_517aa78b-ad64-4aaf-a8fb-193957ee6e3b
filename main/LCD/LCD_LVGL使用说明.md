# LCD + LVGL 使用说明

## 🎯 概述

TFT201A LCD (ST7789) 驱动 + LVGL图形库 + GUI Guider集成的完整解决方案。

**硬件规格**: 240×320 / 320×240 | SPI 20MHz | RGB565 | 可旋转显示

## 🚀 快速开始

### 1. 一站式初始化
```c
// 在main.c中
void app_main(void)
{
    bsp_touch_key_init(NULL);    // 初始化按键
    start_gui_guider_ui();       // 启动GUI系统 (包含完整LCD/LVGL初始化)
}
```

### 2. 初始化流程
```
main.c → start_gui_guider_ui() → lv_port_init()
  ├── bsp_lcd_init()                    // LCD硬件驱动
  ├── bsp_lvgl_init()                   // LVGL库和显示驱动  
  ├── bsp_lcd_register_input_device()   // 输入设备注册
  └── bsp_lvgl_create_task()            // LVGL处理任务
```

## ⚙️ 配置说明

### LCD旋转配置 (重要!)
```c
// 在 bsp_lcd.h 中修改旋转方向
#define LCD_ROTATION  1    // 0=竖屏, 1=横屏, 2=倒置竖屏, 3=倒置横屏
```

**GUI Guider项目推荐**: `LCD_ROTATION = 1` (横屏 320×240)

### 引脚配置
```c
#define LCD_PIN_NUM_MOSI    40    // SPI数据
#define LCD_PIN_NUM_CLK     41    // SPI时钟
#define LCD_PIN_NUM_CS      0     // SPI片选
#define LCD_PIN_NUM_DC      39    // 数据/命令
#define LCD_PIN_NUM_BK_LIGHT 42   // 背光控制
```

### 背光控制配置
```c
// 在 bsp_lcd.h 中配置背光控制方式
#define LCD_BACKLIGHT_PWM   1     // 1=使用PWM调光, 0=使用GPIO开关控制

// PWM调光配置 (当LCD_BACKLIGHT_PWM=1时)
#define LCD_BACKLIGHT_DUTY  80    // PWM占空比 (0-100)
#define LCD_BACKLIGHT_FREQ  5000  // PWM频率 (Hz)
```

**配置说明**:
- **PWM调光模式** (`LCD_BACKLIGHT_PWM=1`): 支持亮度调节，适合有亮度设置功能的应用
- **GPIO开关模式** (`LCD_BACKLIGHT_PWM=0`): 简单的开/关控制，节省资源

## 🎨 GUI Guider集成

### 文件结构
```
main/ui/
├── generated/              # GUI Guider生成的代码 (不要修改)
│   ├── events_init.c       # 事件处理 (需要替换界面切换代码)
│   ├── gui_guider.h        # UI结构定义
│   └── setup_scr_*.c       # 界面设置函数
└── custom/                 # 用户自定义代码 (可以修改)
    ├── custom.h            # 按键控制公共接口
    └── custom.c            # 按键控制系统实现
```

### 关键集成步骤

#### 1. 配置CMakeLists.txt
确保所有GUI Guider生成的文件都添加到编译配置中：
```cmake
set(COMPONENT_SRCS
    # ... 其他文件
    "ui/generated/gui_guider.c"
    "ui/generated/events_init.c"
    "ui/generated/setup_scr_*.c"         # 所有界面文件
    "ui/generated/images/*.c"            # 所有图片文件
    "ui/custom/custom.c"
)

set(COMPONENT_ADD_INCLUDEDIRS
    # ... 其他路径
    "ui/generated"
    "ui/custom"
)
```

#### 2. 添加头文件
在`events_init.c`中添加：
```c
#include "custom.h"  // 添加这一行！
```

#### 2. 替换界面切换代码
```c
// ❌ GUI Guider原始代码
lv_scr_load(guider_ui.Home_Des);

// ✅ 修改后的代码
ui_switch_to_screen(guider_ui.Home_Des, UI_STATE_HOME);
```

#### 3. 修改图片文件的LVGL头文件路径 ⭐ **重要**
在`generated/images/`文件夹中的所有`.c`文件中：
```c
// ❌ GUI Guider生成的路径
#include "lvgl.h"

// ✅ 修改为managed component路径
#include "lvgl__lvgl.h"
```
**说明**: 项目使用managed component方式管理LVGL，头文件路径需要与实际路径一致

## 📋 基础API

### LCD基础操作
```c
bsp_lcd_init();                                    // 初始化
bsp_lcd_backlight_set(80);                         // 背光80%
bsp_lcd_fill_screen(LCD_COLOR_BLUE);               // 蓝色清屏
bsp_lcd_fill_rect(10, 10, 50, 50, LCD_COLOR_RED);  // 红色矩形
```

### LVGL控件操作
```c
// 标签操作
lv_label_set_text(guider_ui.main_label, "新文本");
lv_label_set_text_fmt(guider_ui.status_label, "电池: %d%%", battery_level);

// 按钮操作
lv_obj_add_state(guider_ui.main_btn, LV_STATE_DISABLED);    // 禁用
lv_obj_clear_state(guider_ui.main_btn, LV_STATE_DISABLED);  // 启用

// 进度条操作
lv_bar_set_value(guider_ui.progress_bar, 75, LV_ANIM_ON);

// 图片操作
lv_img_set_src(guider_ui.main_img, &img_logo);
lv_obj_add_flag(guider_ui.main_img, LV_OBJ_FLAG_HIDDEN);    // 隐藏
```

### 界面切换
```c
// ✅ 正确方式：使用统一接口
ui_switch_to_screen(guider_ui.target_screen, UI_STATE_TARGET);

// ❌ 错误方式：直接切换
lv_scr_load(guider_ui.target_screen);  // 状态不同步
```

## 🔧 事件处理示例

### 按钮事件
```c
static void btn_clicked(lv_event_t * e)
{
    lv_event_code_t code = lv_event_get_code(e);
    if(code == LV_EVENT_CLICKED) {
        ESP_LOGI("GUI", "按钮被点击");
        ui_switch_to_screen(guider_ui.next_screen, UI_STATE_NEXT);
    }
}
```

### 定时器事件 (开机界面跳转)
```c
static void progress_timer_cb(lv_timer_t *timer)
{
    // 进度条更新...
    if (progress_value >= 100) {
        // 使用统一接口跳转
        ui_switch_to_screen(guider_ui.Home_Des, UI_STATE_HOME);
    }
}
```

## 🐛 常见问题

### 1. 显示问题
- **显示异常**: 检查引脚连接和旋转配置
- **颜色错误**: 确保`LV_COLOR_16_SWAP = 0`
- **界面变形**: 检查LCD_ROTATION设置

### 2. 界面切换问题
- **按键无响应**: 确保使用`ui_switch_to_screen()`
- **界面切换失效**: 检查是否添加了`custom.h`头文件
- **状态不同步**: 确保没有直接调用`lv_scr_load()`

### 3. 内存问题
- **内存不足**: 减少LVGL缓冲区大小
- **界面创建失败**: 增加LVGL内存池大小

### 4. 性能问题
- **响应慢**: 调整LVGL任务优先级
- **动画卡顿**: 检查SPI时钟频率

## 📊 调试技巧

### 检查界面创建
```c
void debug_ui_creation(void)
{
    ESP_LOGI("GUI", "Start_Open: %p", guider_ui.Start_Open);
    ESP_LOGI("GUI", "Home_Des: %p", guider_ui.Home_Des);
    
    if (guider_ui.Home_Des == NULL) {
        ESP_LOGE("GUI", "❌ Home_Des界面创建失败！");
    } else {
        ESP_LOGI("GUI", "✅ Home_Des界面创建成功");
    }
}
```

### 内存监控
```c
lv_mem_monitor_t mon;
lv_mem_monitor(&mon);
ESP_LOGI("LVGL", "内存使用: %d/%d bytes", 
         mon.total_size - mon.free_size, mon.total_size);
```

## 📁 核心文件

- `bsp_lcd.h/c` - LCD驱动 + LVGL集成
- `lcd_test.h/c` - 测试程序 + GUI启动函数
- `ui/generated/` - GUI Guider生成的界面代码
- `ui/custom/` - 用户自定义的按键控制代码

## 🎉 完成状态

- ✅ LCD驱动 + LVGL集成
- ✅ GUI Guider代码集成
- ✅ 按键控制系统
- ✅ 界面切换管理
- ✅ 旋转支持
- ✅ 一站式初始化

现在可以开始开发您的图形界面应用了！
