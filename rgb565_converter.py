#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RGB565转换器 - 将PNG/JPG图片转换为ESP32可用的RGB565格式
适用于ESP32-S3 ST7789 LCD显示屏
"""

from PIL import Image
import struct
import os
import sys

def convert_to_rgb565(input_path, output_path, width, height):
    """
    将图片转换为RGB565格式的二进制文件
    
    Args:
        input_path: 输入图片路径
        output_path: 输出.bin文件路径
        width: 目标宽度
        height: 目标高度
    """
    try:
        # 打开并调整图片大小
        print(f"正在处理: {input_path}")
        img = Image.open(input_path)
        
        # 调整大小，使用高质量重采样
        img = img.resize((width, height), Image.Resampling.LANCZOS)
        img = img.convert('RGB')
        
        print(f"调整尺寸为: {width}×{height}")
        
        # 转换为RGB565并写入文件
        with open(output_path, 'wb') as f:
            for y in range(height):
                for x in range(width):
                    r, g, b = img.getpixel((x, y))
                    
                    # 转换为RGB565格式
                    # R: 8位 -> 5位 (右移3位)
                    # G: 8位 -> 6位 (右移2位)  
                    # B: 8位 -> 5位 (右移3位)
                    r565 = (r >> 3) << 11
                    g565 = (g >> 2) << 5
                    b565 = b >> 3
                    rgb565 = r565 | g565 | b565
                    
                    # 写入小端格式 (ESP32使用小端)
                    f.write(struct.pack('<H', rgb565))
        
        file_size = os.path.getsize(output_path)
        print(f"转换完成: {output_path} ({file_size} 字节)")
        return True
        
    except Exception as e:
        print(f"转换失败: {e}")
        return False

def batch_convert_flowers():
    """批量转换花朵图片"""
    
    # 定义转换任务
    tasks = [
        # 蒲公英
        ("dandelion_flower.png", "flower_001_thumb.bin", 64, 64),
        ("dandelion_flower.png", "flower_001_full.bin", 150, 150),
        
        # 郁金香  
        ("tulip_flower.png", "flower_002_thumb.bin", 64, 64),
        ("tulip_flower.png", "flower_002_full.bin", 150, 150),
    ]
    
    print("=== ESP32 花朵图片RGB565转换器 ===")
    print("目标格式: RGB565 (16位)")
    print("目标设备: ESP32-S3 + ST7789 LCD")
    print()
    
    success_count = 0
    total_count = len(tasks)
    
    for input_file, output_file, width, height in tasks:
        if os.path.exists(input_file):
            if convert_to_rgb565(input_file, output_file, width, height):
                success_count += 1
            print()
        else:
            print(f"警告: 找不到文件 {input_file}")
            print()
    
    print(f"转换完成: {success_count}/{total_count} 个文件成功")
    
    if success_count > 0:
        print("\n生成的文件:")
        for _, output_file, width, height in tasks:
            if os.path.exists(output_file):
                size = os.path.getsize(output_file)
                expected_size = width * height * 2
                status = "✓" if size == expected_size else "⚠"
                print(f"  {status} {output_file} - {width}×{height} ({size} 字节)")
        
        print("\n📁 文件说明:")
        print("  flower_001_thumb.bin - 蒲公英缩略图 (64×64)")
        print("  flower_001_full.bin  - 蒲公英预览图 (150×150)")
        print("  flower_002_thumb.bin - 郁金香缩略图 (64×64)")
        print("  flower_002_full.bin  - 郁金香预览图 (150×150)")
        
        print("\n🚀 使用方法:")
        print("  1. 将.bin文件复制到SD卡的 /flower/image/ 目录")
        print("  2. 在ESP32代码中调用 read_flower_image() 函数")
        print("  3. 使用 display_flower_image_to_lcd() 显示图片")

def convert_single_image():
    """交互式转换单个图片"""
    print("=== 单个图片转换模式 ===")
    
    input_file = input("请输入图片文件名 (如: my_flower.png): ").strip()
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return
    
    output_file = input("请输入输出文件名 (如: my_flower.bin): ").strip()
    if not output_file.endswith('.bin'):
        output_file += '.bin'
    
    try:
        width = int(input("请输入宽度 (推荐: 64, 150, 296): "))
        height = int(input("请输入高度 (推荐: 64, 150, 240): "))
    except ValueError:
        print("错误: 请输入有效的数字")
        return
    
    convert_to_rgb565(input_file, output_file, width, height)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "single":
            convert_single_image()
        else:
            print("用法:")
            print("  python rgb565_converter.py        # 批量转换花朵图片")
            print("  python rgb565_converter.py single # 交互式转换单个图片")
    else:
        batch_convert_flowers()

if __name__ == "__main__":
    main()
